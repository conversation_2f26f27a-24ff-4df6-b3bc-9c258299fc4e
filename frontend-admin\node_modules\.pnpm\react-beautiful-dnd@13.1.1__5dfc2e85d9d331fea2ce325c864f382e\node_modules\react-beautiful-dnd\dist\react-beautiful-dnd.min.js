!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e=e||self).ReactBeautifulDnd={},e.<PERSON>act,e.ReactDOM)}(this,(function(e,t,r){"use strict";var n="default"in t?t.default:t,i="default"in r?r.default:r;function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function a(){}function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function u(e,t,r){var n=t.map((function(t){var n,i,o=(n=r,i=t.options,l({},n,{},i));return e.addEventListener(t.eventName,t.fn,o),function(){e.removeEventListener(t.eventName,t.fn,o)}}));return function(){n.forEach((function(e){e()}))}}function c(e){this.message=e}function s(e,t){if(!e)throw new c("Invariant failed")}c.prototype.toString=function(){return this.message};var d=function(e){function t(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).callbacks=null,t.unbind=a,t.onWindowError=function(e){var r=t.getCallbacks();r.isDragging()&&r.tryAbort(),e.error instanceof c&&e.preventDefault()},t.getCallbacks=function(){if(!t.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return t.callbacks},t.setCallbacks=function(e){t.callbacks=e},t}o(t,e);var r=t.prototype;return r.componentDidMount=function(){this.unbind=u(window,[{eventName:"error",fn:this.onWindowError}])},r.componentDidCatch=function(e){if(!(e instanceof c))throw e;this.setState({})},r.componentWillUnmount=function(){this.unbind()},r.render=function(){return this.props.children(this.setCallbacks)},t}(n.Component),p=function(e){return e+1},f=function(e,t){var r=e.droppableId===t.droppableId,n=p(e.index),i=p(t.index);return r?"\n      You have moved the item from position "+n+"\n      to position "+i+"\n    ":"\n    You have moved the item from position "+n+"\n    in list "+e.droppableId+"\n    to list "+t.droppableId+"\n    in position "+i+"\n  "},g=function(e,t,r){return t.droppableId===r.droppableId?"\n      The item "+e+"\n      has been combined with "+r.draggableId:"\n      The item "+e+"\n      in list "+t.droppableId+"\n      has been combined with "+r.draggableId+"\n      in list "+r.droppableId+"\n    "},v=function(e){return"\n  The item has returned to its starting position\n  of "+p(e.index)+"\n"},m="\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",b=function(e){return"\n  You have lifted an item in position "+p(e.source.index)+"\n"},h=function(e){var t=e.destination;if(t)return f(e.source,t);var r=e.combine;return r?g(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},y=function(e){if("CANCEL"===e.reason)return"\n      Movement cancelled.\n      "+v(e.source)+"\n    ";var t=e.destination,r=e.combine;return t?"\n      You have dropped the item.\n      "+f(e.source,t)+"\n    ":r?"\n      You have dropped the item.\n      "+g(e.draggableId,e.source,r)+"\n    ":"\n    The item has been dropped while not over a drop area.\n    "+v(e.source)+"\n  "};var x=function(e){var t,r=e.Symbol;return"function"==typeof r?r.observable?t=r.observable:(t=r("observable"),r.observable=t):t="@@observable",t}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof module?module:Function("return this")()),I=function(){return Math.random().toString(36).substring(7).split("").join(".")},D={INIT:"@@redux/INIT"+I(),REPLACE:"@@redux/REPLACE"+I(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+I()}};function w(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function E(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error("It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function.");if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error("Expected the enhancer to be a function.");return r(E)(e,t)}if("function"!=typeof e)throw new Error("Expected the reducer to be a function.");var i=e,o=t,a=[],l=a,u=!1;function c(){l===a&&(l=a.slice())}function s(){if(u)throw new Error("You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");return o}function d(e){if("function"!=typeof e)throw new Error("Expected the listener to be a function.");if(u)throw new Error("You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api-reference/store#subscribelistener for more details.");var t=!0;return c(),l.push(e),function(){if(t){if(u)throw new Error("You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api-reference/store#subscribelistener for more details.");t=!1,c();var r=l.indexOf(e);l.splice(r,1),a=null}}}function p(e){if(!w(e))throw new Error("Actions must be plain objects. Use custom middleware for async actions.");if(void 0===e.type)throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(u)throw new Error("Reducers may not dispatch actions.");try{u=!0,o=i(o,e)}finally{u=!1}for(var t=a=l,r=0;r<t.length;r++){(0,t[r])()}return e}function f(e){if("function"!=typeof e)throw new Error("Expected the nextReducer to be a function.");i=e,p({type:D.REPLACE})}function g(){var e,t=d;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new TypeError("Expected the observer to be an object.");function r(){e.next&&e.next(s())}return r(),{unsubscribe:t(r)}}})[x]=function(){return this},e}return p({type:D.INIT}),(n={dispatch:p,subscribe:d,getState:s,replaceReducer:f})[x]=g,n}function C(e,t){return function(){return t(e.apply(this,arguments))}}function S(e,t){if("function"==typeof e)return C(e,t);if("object"!=typeof e||null===e)throw new Error("bindActionCreators expected an object or a function, instead received "+(null===e?"null":typeof e)+'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?');var r={};for(var n in e){var i=e[n];"function"==typeof i&&(r[n]=C(i,t))}return r}function P(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(e,t){var r=Object.keys(e);return Object.getOwnPropertySymbols&&r.push.apply(r,Object.getOwnPropertySymbols(e)),t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(r,!0).forEach((function(t){P(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function R(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function N(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function B(e,t){return e(t={exports:{}},t.exports),t.exports}var T=B((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,d=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,f=r?Symbol.for("react.suspense"):60113,g=r?Symbol.for("react.suspense_list"):60120,v=r?Symbol.for("react.memo"):60115,m=r?Symbol.for("react.lazy"):60116,b=r?Symbol.for("react.fundamental"):60117,h=r?Symbol.for("react.responder"):60118,y=r?Symbol.for("react.scope"):60119;function x(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case d:case o:case l:case a:case f:return e;default:switch(e=e&&e.$$typeof){case c:case p:case m:case v:case u:return e;default:return t}}case i:return t}}}function I(e){return x(e)===d}t.typeOf=x,t.AsyncMode=s,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=u,t.Element=n,t.ForwardRef=p,t.Fragment=o,t.Lazy=m,t.Memo=v,t.Portal=i,t.Profiler=l,t.StrictMode=a,t.Suspense=f,t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===d||e===l||e===a||e===f||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===v||e.$$typeof===u||e.$$typeof===c||e.$$typeof===p||e.$$typeof===b||e.$$typeof===h||e.$$typeof===y)},t.isAsyncMode=function(e){return I(e)||x(e)===s},t.isConcurrentMode=I,t.isContextConsumer=function(e){return x(e)===c},t.isContextProvider=function(e){return x(e)===u},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return x(e)===p},t.isFragment=function(e){return x(e)===o},t.isLazy=function(e){return x(e)===m},t.isMemo=function(e){return x(e)===v},t.isPortal=function(e){return x(e)===i},t.isProfiler=function(e){return x(e)===l},t.isStrictMode=function(e){return x(e)===a},t.isSuspense=function(e){return x(e)===f}}));N(T);T.typeOf,T.AsyncMode,T.ConcurrentMode,T.ContextConsumer,T.ContextProvider,T.Element,T.ForwardRef,T.Fragment,T.Lazy,T.Memo,T.Portal,T.Profiler,T.StrictMode,T.Suspense,T.isValidElementType,T.isAsyncMode,T.isConcurrentMode,T.isContextConsumer,T.isContextProvider,T.isElement,T.isForwardRef,T.isFragment,T.isLazy,T.isMemo,T.isPortal,T.isProfiler,T.isStrictMode,T.isSuspense;var M=B((function(e,t){}));N(M);M.typeOf,M.AsyncMode,M.ConcurrentMode,M.ContextConsumer,M.ContextProvider,M.Element,M.ForwardRef,M.Fragment,M.Lazy,M.Memo,M.Portal,M.Profiler,M.StrictMode,M.Suspense,M.isValidElementType,M.isAsyncMode,M.isConcurrentMode,M.isContextConsumer,M.isContextProvider,M.isElement,M.isForwardRef,M.isFragment,M.isLazy,M.isMemo,M.isPortal,M.isProfiler,M.isStrictMode,M.isSuspense;var L=B((function(e){e.exports=T})),G=(L.isValidElementType,L.isContextConsumer),_=Object.getOwnPropertySymbols,F=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable;function k(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach((function(e){n[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(e){return!1}})()&&Object.assign,Function.call.bind(Object.prototype.hasOwnProperty);function W(){}function U(){}U.resetWarningCache=W;B((function(e){e.exports=function(){function e(e,t,r,n,i,o){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var r={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:U,resetWarningCache:W};return r.PropTypes=r,r}()}));var H=n.createContext(null);var q=function(e){e()},V=function(){return q},z={notify:function(){}};var $=function(){function e(e,t){this.store=e,this.parentSub=t,this.unsubscribe=null,this.listeners=z,this.handleChangeWrapper=this.handleChangeWrapper.bind(this)}var t=e.prototype;return t.addNestedSub=function(e){return this.trySubscribe(),this.listeners.subscribe(e)},t.notifyNestedSubs=function(){this.listeners.notify()},t.handleChangeWrapper=function(){this.onStateChange&&this.onStateChange()},t.isSubscribed=function(){return Boolean(this.unsubscribe)},t.trySubscribe=function(){this.unsubscribe||(this.unsubscribe=this.parentSub?this.parentSub.addNestedSub(this.handleChangeWrapper):this.store.subscribe(this.handleChangeWrapper),this.listeners=function(){var e=V(),t=null,r=null;return{clear:function(){t=null,r=null},notify:function(){e((function(){for(var e=t;e;)e.callback(),e=e.next}))},get:function(){for(var e=[],r=t;r;)e.push(r),r=r.next;return e},subscribe:function(e){var n=!0,i=r={callback:e,next:null,prev:r};return i.prev?i.prev.next=i:t=i,function(){n&&null!==t&&(n=!1,i.next?i.next.prev=i.prev:r=i.prev,i.prev?i.prev.next=i.next:t=i.next)}}}}())},t.tryUnsubscribe=function(){this.unsubscribe&&(this.unsubscribe(),this.unsubscribe=null,this.listeners.clear(),this.listeners=z)},e}();function Y(e){var r=e.store,i=e.context,o=e.children,a=t.useMemo((function(){var e=new $(r);return e.onStateChange=e.notifyNestedSubs,{store:r,subscription:e}}),[r]),l=t.useMemo((function(){return r.getState()}),[r]);t.useEffect((function(){var e=a.subscription;return e.trySubscribe(),l!==r.getState()&&e.notifyNestedSubs(),function(){e.tryUnsubscribe(),e.onStateChange=null}}),[a,l]);var u=i||H;return n.createElement(u.Provider,{value:a},o)}function J(){return(J=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function X(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}var K={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Q={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Z={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},ee={};function te(e){return L.isMemo(e)?Z:ee[e.$$typeof]||K}ee[L.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ee[L.Memo]=Z;var re=Object.defineProperty,ne=Object.getOwnPropertyNames,ie=Object.getOwnPropertySymbols,oe=Object.getOwnPropertyDescriptor,ae=Object.getPrototypeOf,le=Object.prototype;var ue=function e(t,r,n){if("string"!=typeof r){if(le){var i=ae(r);i&&i!==le&&e(t,i,n)}var o=ne(r);ie&&(o=o.concat(ie(r)));for(var a=te(t),l=te(r),u=0;u<o.length;++u){var c=o[u];if(!(Q[c]||n&&n[c]||l&&l[c]||a&&a[c])){var s=oe(r,c);try{re(t,c,s)}catch(e){}}}}return t},ce="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect,se=[],de=[null,null];function pe(e,t){var r=e[1];return[t.payload,r+1]}function fe(e,t,r){ce((function(){return e.apply(void 0,t)}),r)}function ge(e,t,r,n,i,o,a){e.current=n,t.current=i,r.current=!1,o.current&&(o.current=null,a())}function ve(e,t,r,n,i,o,a,l,u,c){if(e){var s=!1,d=null,p=function(){if(!s){var e,r,p=t.getState();try{e=n(p,i.current)}catch(e){r=e,d=e}r||(d=null),e===o.current?a.current||u():(o.current=e,l.current=e,a.current=!0,c({type:"STORE_UPDATED",payload:{error:r}}))}};r.onStateChange=p,r.trySubscribe(),p();return function(){if(s=!0,r.tryUnsubscribe(),r.onStateChange=null,d)throw d}}}var me=function(){return[null,0]};function be(e,r){void 0===r&&(r={});var i=r,o=i.getDisplayName,a=void 0===o?function(e){return"ConnectAdvanced("+e+")"}:o,l=i.methodName,u=void 0===l?"connectAdvanced":l,c=i.renderCountProp,s=void 0===c?void 0:c,d=i.shouldHandleStateChanges,p=void 0===d||d,f=i.storeKey,g=void 0===f?"store":f,v=(i.withRef,i.forwardRef),m=void 0!==v&&v,b=i.context,h=void 0===b?H:b,y=X(i,["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"]),x=h;return function(r){var i=r.displayName||r.name||"Component",o=a(i),l=J({},y,{getDisplayName:a,methodName:u,renderCountProp:s,shouldHandleStateChanges:p,storeKey:g,displayName:o,wrappedComponentName:i,WrappedComponent:r}),c=y.pure;var d=c?t.useMemo:function(e){return e()};function f(i){var o=t.useMemo((function(){var e=i.forwardedRef,t=X(i,["forwardedRef"]);return[i.context,e,t]}),[i]),a=o[0],u=o[1],c=o[2],s=t.useMemo((function(){return a&&a.Consumer&&G(n.createElement(a.Consumer,null))?a:x}),[a,x]),f=t.useContext(s),g=Boolean(i.store)&&Boolean(i.store.getState)&&Boolean(i.store.dispatch),v=(Boolean(f)&&Boolean(f.store),g?i.store:f.store),m=t.useMemo((function(){return function(t){return e(t.dispatch,l)}(v)}),[v]),b=t.useMemo((function(){if(!p)return de;var e=new $(v,g?null:f.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[v,g,f]),h=b[0],y=b[1],I=t.useMemo((function(){return g?f:J({},f,{subscription:h})}),[g,f,h]),D=t.useReducer(pe,se,me),w=D[0][0],E=D[1];if(w&&w.error)throw w.error;var C=t.useRef(),S=t.useRef(c),P=t.useRef(),O=t.useRef(!1),A=d((function(){return P.current&&c===S.current?P.current:m(v.getState(),c)}),[v,w,c]);fe(ge,[S,C,O,c,A,P,y]),fe(ve,[p,v,h,m,S,C,O,P,y,E],[v,h,m]);var R=t.useMemo((function(){return n.createElement(r,J({},A,{ref:u}))}),[u,r,A]);return t.useMemo((function(){return p?n.createElement(s.Provider,{value:I},R):R}),[s,R,I])}var v=c?n.memo(f):f;if(v.WrappedComponent=r,v.displayName=o,m){var b=n.forwardRef((function(e,t){return n.createElement(v,J({},e,{forwardedRef:t}))}));return b.displayName=o,b.WrappedComponent=r,ue(b,r)}return ue(v,r)}}function he(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function ye(e,t){if(he(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var i=0;i<r.length;i++)if(!Object.prototype.hasOwnProperty.call(t,r[i])||!he(e[r[i]],t[r[i]]))return!1;return!0}function xe(e){return function(t,r){var n=e(t,r);function i(){return n}return i.dependsOnOwnProps=!1,i}}function Ie(e){return null!==e.dependsOnOwnProps&&void 0!==e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function De(e,t){return function(t,r){r.displayName;var n=function(e,t){return n.dependsOnOwnProps?n.mapToProps(e,t):n.mapToProps(e)};return n.dependsOnOwnProps=!0,n.mapToProps=function(t,r){n.mapToProps=e,n.dependsOnOwnProps=Ie(e);var i=n(t,r);return"function"==typeof i&&(n.mapToProps=i,n.dependsOnOwnProps=Ie(i),i=n(t,r)),i},n}}var we=[function(e){return"function"==typeof e?De(e):void 0},function(e){return e?void 0:xe((function(e){return{dispatch:e}}))},function(e){return e&&"object"==typeof e?xe((function(t){return S(e,t)})):void 0}];var Ee=[function(e){return"function"==typeof e?De(e):void 0},function(e){return e?void 0:xe((function(){return{}}))}];function Ce(e,t,r){return J({},r,{},e,{},t)}var Se=[function(e){return"function"==typeof e?function(e){return function(t,r){r.displayName;var n,i=r.pure,o=r.areMergedPropsEqual,a=!1;return function(t,r,l){var u=e(t,r,l);return a?i&&o(u,n)||(n=u):(a=!0,n=u),n}}}(e):void 0},function(e){return e?void 0:function(){return Ce}}];function Pe(e,t,r,n){return function(i,o){return r(e(i,o),t(n,o),o)}}function Oe(e,t,r,n,i){var o,a,l,u,c,s=i.areStatesEqual,d=i.areOwnPropsEqual,p=i.areStatePropsEqual,f=!1;function g(i,f){var g,v,m=!d(f,a),b=!s(i,o);return o=i,a=f,m&&b?(l=e(o,a),t.dependsOnOwnProps&&(u=t(n,a)),c=r(l,u,a)):m?(e.dependsOnOwnProps&&(l=e(o,a)),t.dependsOnOwnProps&&(u=t(n,a)),c=r(l,u,a)):b?(g=e(o,a),v=!p(g,l),l=g,v&&(c=r(l,u,a)),c):c}return function(i,s){return f?g(i,s):(l=e(o=i,a=s),u=t(n,a),c=r(l,u,a),f=!0,c)}}function Ae(e,t){var r=t.initMapStateToProps,n=t.initMapDispatchToProps,i=t.initMergeProps,o=X(t,["initMapStateToProps","initMapDispatchToProps","initMergeProps"]),a=r(e,o),l=n(e,o),u=i(e,o);return(o.pure?Oe:Pe)(a,l,u,e,o)}function Re(e,t,r){for(var n=t.length-1;n>=0;n--){var i=t[n](e);if(i)return i}return function(t,n){throw new Error("Invalid value of type "+typeof e+" for "+r+" argument when connecting component "+n.wrappedComponentName+".")}}function Ne(e,t){return e===t}function Be(e){var t=void 0===e?{}:e,r=t.connectHOC,n=void 0===r?be:r,i=t.mapStateToPropsFactories,o=void 0===i?Ee:i,a=t.mapDispatchToPropsFactories,l=void 0===a?we:a,u=t.mergePropsFactories,c=void 0===u?Se:u,s=t.selectorFactory,d=void 0===s?Ae:s;return function(e,t,r,i){void 0===i&&(i={});var a=i,u=a.pure,s=void 0===u||u,p=a.areStatesEqual,f=void 0===p?Ne:p,g=a.areOwnPropsEqual,v=void 0===g?ye:g,m=a.areStatePropsEqual,b=void 0===m?ye:m,h=a.areMergedPropsEqual,y=void 0===h?ye:h,x=X(a,["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"]),I=Re(e,o,"mapStateToProps"),D=Re(t,l,"mapDispatchToProps"),w=Re(r,c,"mergeProps");return n(d,J({methodName:"connect",getDisplayName:function(e){return"Connect("+e+")"},shouldHandleStateChanges:Boolean(e),initMapStateToProps:I,initMapDispatchToProps:D,initMergeProps:w,pure:s,areStatesEqual:f,areOwnPropsEqual:v,areStatePropsEqual:b,areMergedPropsEqual:y},x))}}var Te,Me=Be();function Le(e,r){var n=t.useState((function(){return{inputs:r,result:e()}}))[0],i=t.useRef(n),o=Boolean(r&&i.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(r,i.current.inputs))?i.current:{inputs:r,result:e()};return t.useEffect((function(){i.current=o}),[o]),o.result}Te=r.unstable_batchedUpdates,q=Te;var Ge=Le,_e=function(e,t){return Le((function(){return e}),t)},Fe={x:0,y:0},je=function(e,t){return{x:e.x+t.x,y:e.y+t.y}},ke=function(e,t){return{x:e.x-t.x,y:e.y-t.y}},We=function(e,t){return e.x===t.x&&e.y===t.y},Ue=function(e){return{x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}},He=function(e,t,r){var n;return void 0===r&&(r=0),(n={})[e]=t,n["x"===e?"y":"x"]=r,n},qe=function(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},Ve=function(e,t){return Math.min.apply(Math,t.map((function(t){return qe(e,t)})))},ze=function(e){return function(t){return{x:e(t.x),y:e(t.y)}}};var $e=function(e){var t=e.top,r=e.right,n=e.bottom,i=e.left;return{top:t,right:r,bottom:n,left:i,width:r-i,height:n-t,x:i,y:t,center:{x:(r+i)/2,y:(n+t)/2}}},Ye=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},Je=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},Xe={top:0,right:0,bottom:0,left:0},Ke=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?Xe:r,i=e.border,o=void 0===i?Xe:i,a=e.padding,l=void 0===a?Xe:a,u=$e(Ye(t,n)),c=$e(Je(t,o)),s=$e(Je(c,l));return{marginBox:u,borderBox:$e(t),paddingBox:c,contentBox:s,margin:n,border:o,padding:l}},Qe=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&function(e,t){if(!e)throw new Error("Invariant failed")}(!1),r},Ze=function(e,t){var r,n,i=e.borderBox,o=e.border,a=e.margin,l=e.padding,u=(n=t,{top:(r=i).top+n.y,left:r.left+n.x,bottom:r.bottom+n.y,right:r.right+n.x});return Ke({borderBox:u,border:o,margin:a,padding:l})},et=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),Ze(e,t)},tt=function(e,t){var r={top:Qe(t.marginTop),right:Qe(t.marginRight),bottom:Qe(t.marginBottom),left:Qe(t.marginLeft)},n={top:Qe(t.paddingTop),right:Qe(t.paddingRight),bottom:Qe(t.paddingBottom),left:Qe(t.paddingLeft)},i={top:Qe(t.borderTopWidth),right:Qe(t.borderRightWidth),bottom:Qe(t.borderBottomWidth),left:Qe(t.borderLeftWidth)};return Ke({borderBox:e,margin:r,padding:n,border:i})},rt=function(e){var t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return tt(t,r)},nt=function(e,t){return{top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}},it=function(e){return[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}]},ot=function(e,t){return t&&t.shouldClipSubject?function(e,t){var r=$e({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r}(t.pageMarginBox,e):$e(e)},at=function(e){var t=e.page,r=e.withPlaceholder,n=e.axis,i=e.frame,o=function(e,t,r){var n;return r&&r.increasedBy?l({},e,((n={})[t.end]=e[t.end]+r.increasedBy[t.line],n)):e}(function(e,t){return t?nt(e,t.scroll.diff.displacement):e}(t.marginBox,i),n,r);return{page:t,withPlaceholder:r,active:ot(o,i)}},lt=function(e,t){e.frame||s(!1);var r=e.frame,n=ke(t,r.scroll.initial),i=Ue(n),o=l({},r,{scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:i},max:r.scroll.max}});return l({},e,{frame:o,subject:at({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:o})})};function ut(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function ct(e,t){var r;void 0===t&&(t=ut);var n,i=[],o=!1;return function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return o&&r===this&&t(a,i)?n:(n=e.apply(this,a),o=!0,r=this,i=a,n)}}function st(e){return Object.values?Object.values(e):Object.keys(e).map((function(t){return e[t]}))}function dt(e,t){if(e.findIndex)return e.findIndex(t);for(var r=0;r<e.length;r++)if(t(e[r]))return r;return-1}function pt(e,t){if(e.find)return e.find(t);var r=dt(e,t);return-1!==r?e[r]:void 0}function ft(e){return Array.prototype.slice.call(e)}var gt=ct((function(e){return e.reduce((function(e,t){return e[t.descriptor.id]=t,e}),{})})),vt=ct((function(e){return e.reduce((function(e,t){return e[t.descriptor.id]=t,e}),{})})),mt=ct((function(e){return st(e)})),bt=ct((function(e){return st(e)})),ht=ct((function(e,t){return bt(t).filter((function(t){return e===t.descriptor.droppableId})).sort((function(e,t){return e.descriptor.index-t.descriptor.index}))}));function yt(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function xt(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var It=ct((function(e,t){return t.filter((function(t){return t.descriptor.id!==e.descriptor.id}))})),Dt=function(e,t){return e.descriptor.droppableId===t.descriptor.id},wt={point:Fe,value:0},Et={invisible:{},visible:{},all:[]},Ct={displaced:Et,displacedBy:wt,at:null},St=function(e,t){return function(r){return e<=r&&r<=t}},Pt=function(e){var t=St(e.top,e.bottom),r=St(e.left,e.right);return function(n){if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;var i=t(n.top)||t(n.bottom),o=r(n.left)||r(n.right);if(i&&o)return!0;var a=n.top<e.top&&n.bottom>e.bottom,l=n.left<e.left&&n.right>e.right;return!(!a||!l)||(a&&o||l&&i)}},Ot=function(e){var t=St(e.top,e.bottom),r=St(e.left,e.right);return function(e){return t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)}},At={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},Rt={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},Nt=function(e){var t=e.target,r=e.destination,n=e.viewport,i=e.withDroppableDisplacement,o=e.isVisibleThroughFrameFn,a=i?function(e,t){var r=t.frame?t.frame.scroll.diff.displacement:Fe;return nt(e,r)}(t,r):t;return function(e,t,r){return!!t.subject.active&&r(t.subject.active)(e)}(a,r,o)&&function(e,t,r){return r(t)(e)}(a,n,o)},Bt=function(e){return Nt(l({},e,{isVisibleThroughFrameFn:Pt}))},Tt=function(e){return Nt(l({},e,{isVisibleThroughFrameFn:Ot}))};function Mt(e){var t=e.afterDragging,r=e.destination,n=e.displacedBy,i=e.viewport,o=e.forceShouldAnimate,a=e.last;return t.reduce((function(e,t){var l=function(e,t){var r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return $e(Ye(r,n))}(t,n),u=t.descriptor.id;if(e.all.push(u),!Bt({target:l,destination:r,viewport:i,withDroppableDisplacement:!0}))return e.invisible[t.descriptor.id]=!0,e;var c={draggableId:u,shouldAnimate:function(e,t,r){if("boolean"==typeof r)return r;if(!t)return!0;var n=t.invisible,i=t.visible;if(n[e])return!1;var o=i[e];return!o||o.shouldAnimate}(u,a,o)};return e.visible[u]=c,e}),{all:[],visible:{},invisible:{}})}function Lt(e){var t=e.insideDestination,r=e.inHomeList,n=e.displacedBy,i=e.destination,o=function(e,t){if(!e.length)return 0;var r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(t,{inHomeList:r});return{displaced:Et,displacedBy:n,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:o}}}}function Gt(e){var t=e.draggable,r=e.insideDestination,n=e.destination,i=e.viewport,o=e.displacedBy,a=e.last,l=e.index,u=e.forceShouldAnimate,c=Dt(t,n);if(null==l)return Lt({insideDestination:r,inHomeList:c,displacedBy:o,destination:n});var s=pt(r,(function(e){return e.descriptor.index===l}));if(!s)return Lt({insideDestination:r,inHomeList:c,displacedBy:o,destination:n});var d=It(t,r),p=r.indexOf(s);return{displaced:Mt({afterDragging:d.slice(p),destination:n,displacedBy:o,last:a,viewport:i.frame,forceShouldAnimate:u}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function _t(e,t){return Boolean(t.effected[e])}var Ft=function(e){var t=e.isMovingForward,r=e.isInHomeList,n=e.draggable,i=e.draggables,o=e.destination,a=e.insideDestination,l=e.previousImpact,u=e.viewport,c=e.afterCritical,d=l.at;if(d||s(!1),"REORDER"===d.type){var p=function(e){var t=e.isMovingForward,r=e.isInHomeList,n=e.insideDestination,i=e.location;if(!n.length)return null;var o=i.index,a=t?o+1:o-1,l=n[0].descriptor.index,u=n[n.length-1].descriptor.index;return a<l?null:a>(r?u:u+1)?null:a}({isMovingForward:t,isInHomeList:r,location:d.destination,insideDestination:a});return null==p?null:Gt({draggable:n,insideDestination:a,destination:o,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:p})}var f=function(e){var t=e.isMovingForward,r=e.destination,n=e.draggables,i=e.combine,o=e.afterCritical;if(!r.isCombineEnabled)return null;var a=i.draggableId,l=n[a].descriptor.index;return _t(a,o)?t?l:l-1:t?l+1:l}({isMovingForward:t,destination:o,displaced:l.displaced,draggables:i,combine:d.combine,afterCritical:c});return null==f?null:Gt({draggable:n,insideDestination:a,destination:o,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:f})},jt=function(e){var t=e.afterCritical,r=e.impact,n=e.draggables,i=xt(r);i||s(!1);var o=i.draggableId,a=n[o].page.borderBox.center,l=function(e){var t=e.displaced,r=e.afterCritical,n=e.combineWith,i=e.displacedBy,o=Boolean(t.visible[n]||t.invisible[n]);return _t(n,r)?o?Fe:Ue(i.point):o?i.point:Fe}({displaced:r.displaced,afterCritical:t,combineWith:o,displacedBy:r.displacedBy});return je(a,l)},kt=function(e,t){return t.margin[e.start]+t.borderBox[e.size]/2},Wt=function(e,t,r){return t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2},Ut=function(e){var t=e.axis,r=e.moveRelativeTo,n=e.isMoving;return He(t.line,r.marginBox[t.end]+kt(t,n),Wt(t,r.marginBox,n))},Ht=function(e){var t=e.axis,r=e.moveRelativeTo,n=e.isMoving;return He(t.line,r.marginBox[t.start]-function(e,t){return t.margin[e.end]+t.borderBox[e.size]/2}(t,n),Wt(t,r.marginBox,n))},qt=function(e){var t=e.impact,r=e.draggable,n=e.draggables,i=e.droppable,o=e.afterCritical,a=ht(i.descriptor.id,n),l=r.page,u=i.axis;if(!a.length)return function(e){var t=e.axis,r=e.moveInto,n=e.isMoving;return He(t.line,r.contentBox[t.start]+kt(t,n),Wt(t,r.contentBox,n))}({axis:u,moveInto:i.page,isMoving:l});var c=t.displaced,s=t.displacedBy,d=c.all[0];if(d){var p=n[d];if(_t(d,o))return Ht({axis:u,moveRelativeTo:p.page,isMoving:l});var f=Ze(p.page,s.point);return Ht({axis:u,moveRelativeTo:f,isMoving:l})}var g=a[a.length-1];if(g.descriptor.id===r.descriptor.id)return l.borderBox.center;if(_t(g.descriptor.id,o)){var v=Ze(g.page,Ue(o.displacedBy.point));return Ut({axis:u,moveRelativeTo:v,isMoving:l})}return Ut({axis:u,moveRelativeTo:g.page,isMoving:l})},Vt=function(e,t){var r=e.frame;return r?je(t,r.scroll.diff.displacement):t},zt=function(e){var t=function(e){var t=e.impact,r=e.draggable,n=e.droppable,i=e.draggables,o=e.afterCritical,a=r.page.borderBox.center,l=t.at;return n&&l?"REORDER"===l.type?qt({impact:t,draggable:r,draggables:i,droppable:n,afterCritical:o}):jt({impact:t,draggables:i,afterCritical:o}):a}(e),r=e.droppable;return r?Vt(r,t):t},$t=function(e,t){var r=ke(t,e.scroll.initial),n=Ue(r);return{frame:$e({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function Yt(e,t){return e.map((function(e){return t[e]}))}var Jt=function(e){var t=e.pageBorderBoxCenter,r=e.draggable,n=function(e,t){return je(e.scroll.diff.displacement,t)}(e.viewport,t),i=ke(n,r.page.borderBox.center);return je(r.client.borderBox.center,i)},Xt=function(e){var t=e.draggable,r=e.destination,n=e.newPageBorderBoxCenter,i=e.viewport,o=e.withDroppableDisplacement,a=e.onlyOnMainAxis,u=void 0!==a&&a,c=ke(n,t.page.borderBox.center),s={target:nt(t.page.borderBox,c),destination:r,withDroppableDisplacement:o,viewport:i};return u?function(e){return Nt(l({},e,{isVisibleThroughFrameFn:(t=e.destination.axis,function(e){var r=St(e.top,e.bottom),n=St(e.left,e.right);return function(e){return t===At?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)}})}));var t}(s):Tt(s)},Kt=function(e){var t=e.isMovingForward,r=e.draggable,n=e.destination,i=e.draggables,o=e.previousImpact,a=e.viewport,u=e.previousPageBorderBoxCenter,c=e.previousClientSelection,d=e.afterCritical;if(!n.isEnabled)return null;var p=ht(n.descriptor.id,i),f=Dt(r,n),g=function(e){var t=e.isMovingForward,r=e.draggable,n=e.destination,i=e.insideDestination,o=e.previousImpact;if(!n.isCombineEnabled)return null;if(!yt(o))return null;function a(e){var t={type:"COMBINE",combine:{draggableId:e,droppableId:n.descriptor.id}};return l({},o,{at:t})}var u=o.displaced.all,c=u.length?u[0]:null;if(t)return c?a(c):null;var d=It(r,i);if(!c)return d.length?a(d[d.length-1].descriptor.id):null;var p=dt(d,(function(e){return e.descriptor.id===c}));-1===p&&s(!1);var f=p-1;return f<0?null:a(d[f].descriptor.id)}({isMovingForward:t,draggable:r,destination:n,insideDestination:p,previousImpact:o})||Ft({isMovingForward:t,isInHomeList:f,draggable:r,draggables:i,destination:n,insideDestination:p,previousImpact:o,viewport:a,afterCritical:d});if(!g)return null;var v=zt({impact:g,draggable:r,droppable:n,draggables:i,afterCritical:d});if(Xt({draggable:r,destination:n,newPageBorderBoxCenter:v,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:Jt({pageBorderBoxCenter:v,draggable:r,viewport:a}),impact:g,scrollJumpRequest:null};var m=ke(v,u);return{clientSelection:c,impact:function(e){var t=e.impact,r=e.viewport,n=e.destination,i=e.draggables,o=e.maxScrollChange,a=$t(r,je(r.scroll.current,o)),u=n.frame?lt(n,je(n.frame.scroll.current,o)):n,c=t.displaced,s=Mt({afterDragging:Yt(c.all,i),destination:n,displacedBy:t.displacedBy,viewport:a.frame,last:c,forceShouldAnimate:!1}),d=Mt({afterDragging:Yt(c.all,i),destination:u,displacedBy:t.displacedBy,viewport:r.frame,last:c,forceShouldAnimate:!1}),p={},f={},g=[c,s,d];return c.all.forEach((function(e){var t=function(e,t){for(var r=0;r<t.length;r++){var n=t[r].visible[e];if(n)return n}return null}(e,g);t?f[e]=t:p[e]=!0})),l({},t,{displaced:{all:c.all,invisible:p,visible:f}})}({impact:g,viewport:a,destination:n,draggables:i,maxScrollChange:m}),scrollJumpRequest:m}},Qt=function(e){var t=e.subject.active;return t||s(!1),t},Zt=function(e,t){var r=e.page.borderBox.center;return _t(e.descriptor.id,t)?ke(r,t.displacedBy.point):r},er=function(e,t){var r=e.page.borderBox;return _t(e.descriptor.id,t)?nt(r,Ue(t.displacedBy.point)):r},tr=ct((function(e,t){var r=t[e.line];return{value:r,point:He(e.line,r)}})),rr=function(e,t){return l({},e,{scroll:l({},e.scroll,{max:t})})},nr=function(e,t,r){var n=e.frame;Dt(t,e)&&s(!1),e.subject.withPlaceholder&&s(!1);var i=tr(e.axis,t.displaceBy).point,o=function(e,t,r){var n=e.axis;if("virtual"===e.descriptor.mode)return He(n.line,t[n.line]);var i=e.subject.page.contentBox[n.size],o=ht(e.descriptor.id,r).reduce((function(e,t){return e+t.client.marginBox[n.size]}),0)+t[n.line]-i;return o<=0?null:He(n.line,o)}(e,i,r),a={placeholderSize:i,increasedBy:o,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n)return l({},e,{subject:at({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame})});var u=o?je(n.scroll.max,o):n.scroll.max,c=rr(n,u);return l({},e,{subject:at({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:c}),frame:c})},ir=function(e){var t=e.isMovingForward,r=e.previousPageBorderBoxCenter,n=e.draggable,i=e.isOver,o=e.draggables,a=e.droppables,l=e.viewport,u=e.afterCritical,c=function(e){var t=e.isMovingForward,r=e.pageBorderBoxCenter,n=e.source,i=e.droppables,o=e.viewport,a=n.subject.active;if(!a)return null;var l=n.axis,u=St(a[l.start],a[l.end]),c=mt(i).filter((function(e){return e!==n})).filter((function(e){return e.isEnabled})).filter((function(e){return Boolean(e.subject.active)})).filter((function(e){return Pt(o.frame)(Qt(e))})).filter((function(e){var r=Qt(e);return t?a[l.crossAxisEnd]<r[l.crossAxisEnd]:r[l.crossAxisStart]<a[l.crossAxisStart]})).filter((function(e){var t=Qt(e),r=St(t[l.start],t[l.end]);return u(t[l.start])||u(t[l.end])||r(a[l.start])||r(a[l.end])})).sort((function(e,r){var n=Qt(e)[l.crossAxisStart],i=Qt(r)[l.crossAxisStart];return t?n-i:i-n})).filter((function(e,t,r){return Qt(e)[l.crossAxisStart]===Qt(r[0])[l.crossAxisStart]}));if(!c.length)return null;if(1===c.length)return c[0];var s=c.filter((function(e){return St(Qt(e)[l.start],Qt(e)[l.end])(r[l.line])}));return 1===s.length?s[0]:s.length>1?s.sort((function(e,t){return Qt(e)[l.start]-Qt(t)[l.start]}))[0]:c.sort((function(e,t){var n=Ve(r,it(Qt(e))),i=Ve(r,it(Qt(t)));return n!==i?n-i:Qt(e)[l.start]-Qt(t)[l.start]}))[0]}({isMovingForward:t,pageBorderBoxCenter:r,source:i,droppables:a,viewport:l});if(!c)return null;var s=ht(c.descriptor.id,o),d=function(e){var t=e.previousPageBorderBoxCenter,r=e.moveRelativeTo,n=e.insideDestination,i=e.draggable,o=e.draggables,a=e.destination,l=e.viewport,u=e.afterCritical;if(!r){if(n.length)return null;var c={displaced:Et,displacedBy:wt,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:0}}},s=zt({impact:c,draggable:i,droppable:a,draggables:o,afterCritical:u}),d=Dt(i,a)?a:nr(a,i,o);return Xt({draggable:i,destination:d,newPageBorderBoxCenter:s,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?c:null}var p,f=Boolean(t[a.axis.line]<=r.page.borderBox.center[a.axis.line]),g=(p=r.descriptor.index,r.descriptor.id===i.descriptor.id?p:f?p:p+1),v=tr(a.axis,i.displaceBy);return Gt({draggable:i,insideDestination:n,destination:a,viewport:l,displacedBy:v,last:Et,index:g})}({previousPageBorderBoxCenter:r,destination:c,draggable:n,draggables:o,moveRelativeTo:function(e){var t=e.pageBorderBoxCenter,r=e.viewport,n=e.destination,i=e.insideDestination,o=e.afterCritical;return i.filter((function(e){return Tt({target:er(e,o),destination:n,viewport:r.frame,withDroppableDisplacement:!0})})).sort((function(e,r){var i=qe(t,Vt(n,Zt(e,o))),a=qe(t,Vt(n,Zt(r,o)));return i<a?-1:a<i?1:e.descriptor.index-r.descriptor.index}))[0]||null}({pageBorderBoxCenter:r,viewport:l,destination:c,insideDestination:s,afterCritical:u}),insideDestination:s,viewport:l,afterCritical:u});if(!d)return null;var p=zt({impact:d,draggable:n,droppable:c,draggables:o,afterCritical:u});return{clientSelection:Jt({pageBorderBoxCenter:p,draggable:n,viewport:l}),impact:d,scrollJumpRequest:null}},or=function(e){var t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null},ar=function(e){var t=e.state,r=e.type,n=function(e,t){var r=or(e);return r?t[r]:null}(t.impact,t.dimensions.droppables),i=Boolean(n),o=t.dimensions.droppables[t.critical.droppable.id],a=n||o,l=a.axis.direction,u="vertical"===l&&("MOVE_UP"===r||"MOVE_DOWN"===r)||"horizontal"===l&&("MOVE_LEFT"===r||"MOVE_RIGHT"===r);if(u&&!i)return null;var c="MOVE_DOWN"===r||"MOVE_RIGHT"===r,s=t.dimensions.draggables[t.critical.draggable.id],d=t.current.page.borderBoxCenter,p=t.dimensions,f=p.draggables,g=p.droppables;return u?Kt({isMovingForward:c,previousPageBorderBoxCenter:d,draggable:s,destination:a,draggables:f,viewport:t.viewport,previousClientSelection:t.current.client.selection,previousImpact:t.impact,afterCritical:t.afterCritical}):ir({isMovingForward:c,previousPageBorderBoxCenter:d,draggable:s,isOver:a,draggables:f,droppables:g,viewport:t.viewport,afterCritical:t.afterCritical})};function lr(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function ur(e){var t=St(e.top,e.bottom),r=St(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}function cr(e){var t=e.pageBorderBox,r=e.draggable,n=e.droppables,i=mt(n).filter((function(e){if(!e.isEnabled)return!1;var r,n,i=e.subject.active;if(!i)return!1;if(n=i,!((r=t).left<n.right&&r.right>n.left&&r.top<n.bottom&&r.bottom>n.top))return!1;if(ur(i)(t.center))return!0;var o=e.axis,a=i.center[o.crossAxisLine],l=t[o.crossAxisStart],u=t[o.crossAxisEnd],c=St(i[o.crossAxisStart],i[o.crossAxisEnd]),s=c(l),d=c(u);return!s&&!d||(s?l<a:u>a)}));return i.length?1===i.length?i[0].descriptor.id:function(e){var t=e.pageBorderBox,r=e.draggable,n=e.candidates,i=r.page.borderBox.center,o=n.map((function(e){var r=e.axis,n=He(e.axis.line,t.center[r.line],e.page.borderBox.center[r.crossAxisLine]);return{id:e.descriptor.id,distance:qe(i,n)}})).sort((function(e,t){return t.distance-e.distance}));return o[0]?o[0].id:null}({pageBorderBox:t,draggable:r,candidates:i}):null}var sr=function(e,t){return $e(nt(e,t))};function dr(e){var t=e.displaced,r=e.id;return Boolean(t.visible[r]||t.invisible[r])}var pr=function(e){var t=e.pageOffset,r=e.draggable,n=e.draggables,i=e.droppables,o=e.previousImpact,a=e.viewport,l=e.afterCritical,u=sr(r.page.borderBox,t),c=cr({pageBorderBox:u,draggable:r,droppables:i});if(!c)return Ct;var s=i[c],d=ht(s.descriptor.id,n),p=function(e,t){var r=e.frame;return r?sr(t,r.scroll.diff.value):t}(s,u);return function(e){var t=e.draggable,r=e.pageBorderBoxWithDroppableScroll,n=e.previousImpact,i=e.destination,o=e.insideDestination,a=e.afterCritical;if(!i.isCombineEnabled)return null;var l=i.axis,u=tr(i.axis,t.displaceBy),c=u.value,s=r[l.start],d=r[l.end],p=pt(It(t,o),(function(e){var t=e.descriptor.id,r=e.page.borderBox,i=r[l.size]/4,o=_t(t,a),u=dr({displaced:n.displaced,id:t});return o?u?d>r[l.start]+i&&d<r[l.end]-i:s>r[l.start]-c+i&&s<r[l.end]-c-i:u?d>r[l.start]+c+i&&d<r[l.end]+c-i:s>r[l.start]+i&&s<r[l.end]-i}));return p?{displacedBy:u,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:p.descriptor.id,droppableId:i.descriptor.id}}}:null}({pageBorderBoxWithDroppableScroll:p,draggable:r,previousImpact:o,destination:s,insideDestination:d,afterCritical:l})||function(e){var t=e.pageBorderBoxWithDroppableScroll,r=e.draggable,n=e.destination,i=e.insideDestination,o=e.last,a=e.viewport,l=e.afterCritical,u=n.axis,c=tr(n.axis,r.displaceBy),s=c.value,d=t[u.start],p=t[u.end],f=function(e){var t=e.draggable,r=e.closest,n=e.inHomeList;return r?n&&r.descriptor.index>t.descriptor.index?r.descriptor.index-1:r.descriptor.index:null}({draggable:r,closest:pt(It(r,i),(function(e){var t=e.descriptor.id,r=e.page.borderBox.center[u.line],n=_t(t,l),i=dr({displaced:o,id:t});return n?i?p<=r:d<r-s:i?p<=r+s:d<r})),inHomeList:Dt(r,n)});return Gt({draggable:r,insideDestination:i,destination:n,viewport:a,last:o,displacedBy:c,index:f})}({pageBorderBoxWithDroppableScroll:p,draggable:r,destination:s,insideDestination:d,last:o.displaced,viewport:a,afterCritical:l})},fr=function(e,t){var r;return l({},e,((r={})[t.descriptor.id]=t,r))},gr=function(e){var t=e.previousImpact,r=e.impact,n=e.droppables,i=or(t),o=or(r);if(!i)return n;if(i===o)return n;var a=n[i];if(!a.subject.withPlaceholder)return n;var u=function(e){var t=e.subject.withPlaceholder;t||s(!1);var r=e.frame;if(!r)return l({},e,{subject:at({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null})});var n=t.oldFrameMaxScroll;n||s(!1);var i=rr(r,n);return l({},e,{subject:at({page:e.subject.page,axis:e.axis,frame:i,withPlaceholder:null}),frame:i})}(a);return fr(n,u)},vr=function(e){var t=e.state,r=e.clientSelection,n=e.dimensions,i=e.viewport,o=e.impact,a=e.scrollJumpRequest,u=i||t.viewport,c=n||t.dimensions,s=r||t.current.client.selection,d=ke(s,t.initial.client.selection),p={offset:d,selection:s,borderBoxCenter:je(t.initial.client.borderBoxCenter,d)},f={selection:je(p.selection,u.scroll.current),borderBoxCenter:je(p.borderBoxCenter,u.scroll.current),offset:je(p.offset,u.scroll.diff.value)},g={client:p,page:f};if("COLLECTING"===t.phase)return l({phase:"COLLECTING"},t,{dimensions:c,viewport:u,current:g});var v=c.draggables[t.critical.draggable.id],m=o||pr({pageOffset:f.offset,draggable:v,draggables:c.draggables,droppables:c.droppables,previousImpact:t.impact,viewport:u,afterCritical:t.afterCritical}),b=function(e){var t=e.draggable,r=e.draggables,n=e.droppables,i=e.previousImpact,o=e.impact,a=gr({previousImpact:i,impact:o,droppables:n}),l=or(o);if(!l)return a;var u=n[l];if(Dt(t,u))return a;if(u.subject.withPlaceholder)return a;var c=nr(u,t,r);return fr(a,c)}({draggable:v,impact:m,previousImpact:t.impact,draggables:c.draggables,droppables:c.droppables});return l({},t,{current:g,dimensions:{draggables:c.draggables,droppables:b},impact:m,viewport:u,scrollJumpRequest:a||null,forceShouldAnimate:!a&&null})};var mr=function(e){var t=e.impact,r=e.viewport,n=e.draggables,i=e.destination,o=e.forceShouldAnimate,a=t.displaced;return l({},t,{displaced:Mt({afterDragging:function(e,t){return e.map((function(e){return t[e]}))}(a.all,n),destination:i,displacedBy:t.displacedBy,viewport:r.frame,forceShouldAnimate:o,last:a})})},br=function(e){var t=e.impact,r=e.draggable,n=e.droppable,i=e.draggables,o=e.viewport,a=e.afterCritical,l=zt({impact:t,draggable:r,draggables:i,droppable:n,afterCritical:a});return Jt({pageBorderBoxCenter:l,draggable:r,viewport:o})},hr=function(e){var t=e.state,r=e.dimensions,n=e.viewport;"SNAP"!==t.movementMode&&s(!1);var i=t.impact,o=n||t.viewport,a=r||t.dimensions,l=a.draggables,u=a.droppables,c=l[t.critical.draggable.id],d=or(i);d||s(!1);var p=u[d],f=mr({impact:i,viewport:o,destination:p,draggables:l}),g=br({impact:f,draggable:c,droppable:p,draggables:l,viewport:o,afterCritical:t.afterCritical});return vr({impact:f,clientSelection:g,state:t,dimensions:a,viewport:o})},yr=function(e){var t=e.draggable,r=e.home,n=e.draggables,i=e.viewport,o=tr(r.axis,t.displaceBy),a=ht(r.descriptor.id,n),l=a.indexOf(t);-1===l&&s(!1);var u,c=a.slice(l+1),d=c.reduce((function(e,t){return e[t.descriptor.id]=!0,e}),{}),p={inVirtualList:"virtual"===r.descriptor.mode,displacedBy:o,effected:d};return{impact:{displaced:Mt({afterDragging:c,destination:r,displacedBy:o,last:null,viewport:i.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:(u=t.descriptor,{index:u.index,droppableId:u.droppableId})}},afterCritical:p}},xr=function(e){var t=e.additions,r=e.updatedDroppables,n=e.viewport,i=n.scroll.diff.value;return t.map((function(e){var t=e.descriptor.droppableId,o=function(e){var t=e.frame;return t||s(!1),t}(r[t]).scroll.diff.value;return function(e){var t=e.draggable,r=e.offset,n=e.initialWindowScroll,i=Ze(t.client,r),o=et(i,n);return l({},t,{placeholder:l({},t.placeholder,{client:i}),client:i,page:o})}({draggable:e,offset:je(i,o),initialWindowScroll:n.scroll.initial})}))},Ir=function(e){return"SNAP"===e.movementMode},Dr=function(e,t,r){var n=function(e,t){return{draggables:e.draggables,droppables:fr(e.droppables,t)}}(e.dimensions,t);return!Ir(e)||r?vr({state:e,dimensions:n}):hr({state:e,dimensions:n})};function wr(e){return e.isDragging&&"SNAP"===e.movementMode?l({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var Er={phase:"IDLE",completed:null,shouldFlush:!1},Cr=function(e,t){if(void 0===e&&(e=Er),"FLUSH"===t.type)return l({},Er,{shouldFlush:!0});if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&s(!1);var r=t.payload,n=r.critical,i=r.clientSelection,o=r.viewport,a=r.dimensions,u=r.movementMode,c=a.draggables[n.draggable.id],d=a.droppables[n.droppable.id],p={selection:i,borderBoxCenter:c.client.borderBox.center,offset:Fe},f={client:p,page:{selection:je(p.selection,o.scroll.initial),borderBoxCenter:je(p.selection,o.scroll.initial),offset:je(p.selection,o.scroll.diff.value)}},g=mt(a.droppables).every((function(e){return!e.isFixedOnPage})),v=yr({draggable:c,home:d,draggables:a.draggables,viewport:o}),m=v.impact;return{phase:"DRAGGING",isDragging:!0,critical:n,movementMode:u,dimensions:a,initial:f,current:f,isWindowScrollAllowed:g,impact:m,afterCritical:v.afterCritical,onLiftImpact:m,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&s(!1),l({phase:"COLLECTING"},e,{phase:"COLLECTING"}));if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&s(!1),function(e){var t=e.state,r=e.published,n=r.modified.map((function(e){var r=t.dimensions.droppables[e.droppableId];return lt(r,e.scroll)})),i=l({},t.dimensions.droppables,{},gt(n)),o=vt(xr({additions:r.additions,updatedDroppables:i,viewport:t.viewport})),a=l({},t.dimensions.draggables,{},o);r.removals.forEach((function(e){delete a[e]}));var u={droppables:i,draggables:a},c=or(t.impact),s=c?u.droppables[c]:null,d=u.draggables[t.critical.draggable.id],p=u.droppables[t.critical.droppable.id],f=yr({draggable:d,home:p,draggables:a,viewport:t.viewport}),g=f.impact,v=f.afterCritical,m=s&&s.isCombineEnabled?t.impact:g,b=pr({pageOffset:t.current.page.offset,draggable:u.draggables[t.critical.draggable.id],draggables:u.draggables,droppables:u.droppables,previousImpact:m,viewport:t.viewport,afterCritical:v}),h=l({phase:"DRAGGING"},t,{phase:"DRAGGING",impact:b,onLiftImpact:g,dimensions:u,afterCritical:v,forceShouldAnimate:!1});return"COLLECTING"===t.phase?h:l({phase:"DROP_PENDING"},h,{phase:"DROP_PENDING",reason:t.reason,isWaiting:!1})}({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;lr(e)||s(!1);var b=t.payload.client;return We(b,e.current.client.selection)?e:vr({state:e,clientSelection:b,impact:Ir(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return wr(e);if("COLLECTING"===e.phase)return wr(e);lr(e)||s(!1);var h=t.payload,y=h.id,x=h.newScroll,I=e.dimensions.droppables[y];if(!I)return e;var D=lt(I,x);return Dr(e,D,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;lr(e)||s(!1);var w=t.payload,E=w.id,C=w.isEnabled,S=e.dimensions.droppables[E];S||s(!1),S.isEnabled===C&&s(!1);var P=l({},S,{isEnabled:C});return Dr(e,P,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;lr(e)||s(!1);var O=t.payload,A=O.id,R=O.isCombineEnabled,N=e.dimensions.droppables[A];N||s(!1),N.isCombineEnabled===R&&s(!1);var B=l({},N,{isCombineEnabled:R});return Dr(e,B,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;lr(e)||s(!1),e.isWindowScrollAllowed||s(!1);var T=t.payload.newScroll;if(We(e.viewport.scroll.current,T))return wr(e);var M=$t(e.viewport,T);return Ir(e)?hr({state:e,viewport:M}):vr({state:e,viewport:M})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!lr(e))return e;var L=t.payload.maxScroll;if(We(L,e.viewport.scroll.max))return e;var G=l({},e.viewport,{scroll:l({},e.viewport.scroll,{max:L})});return l({phase:"DRAGGING"},e,{viewport:G})}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&s(!1);var _=ar({state:e,type:t.type});return _?vr({state:e,impact:_.impact,clientSelection:_.clientSelection,scrollJumpRequest:_.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){var F=t.payload.reason;return"COLLECTING"!==e.phase&&s(!1),l({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:F})}if("DROP_ANIMATE"===t.type){var j=t.payload,k=j.completed,W=j.dropDuration,U=j.newHomeClientOffset;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&s(!1),{phase:"DROP_ANIMATING",completed:k,dropDuration:W,newHomeClientOffset:U,dimensions:e.dimensions}}return"DROP_COMPLETE"===t.type?{phase:"IDLE",completed:t.payload.completed,shouldFlush:!1}:e},Sr=function(e){return{type:"PUBLISH_WHILE_DRAGGING",payload:e}},Pr=function(){return{type:"COLLECTION_STARTING",payload:null}},Or=function(e){return{type:"UPDATE_DROPPABLE_SCROLL",payload:e}},Ar=function(e){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}},Rr=function(e){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}},Nr=function(e){return{type:"MOVE",payload:e}},Br=function(){return{type:"MOVE_UP",payload:null}},Tr=function(){return{type:"MOVE_DOWN",payload:null}},Mr=function(){return{type:"MOVE_RIGHT",payload:null}},Lr=function(){return{type:"MOVE_LEFT",payload:null}},Gr=function(e){return{type:"DROP_COMPLETE",payload:e}},_r=function(e){return{type:"DROP",payload:e}},Fr=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},jr="cubic-bezier(.2,1,.1,1)",kr={drop:0,combining:.7},Wr={drop:.75},Ur=.2+"s "+"cubic-bezier(0.2, 0, 0, 1)",Hr={fluid:"opacity "+Ur,snap:"transform "+Ur+", opacity "+Ur,drop:function(e){var t=e+"s "+jr;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+Ur,placeholder:"height "+Ur+", width "+Ur+", margin "+Ur},qr=function(e){return We(e,Fe)?null:"translate("+e.x+"px, "+e.y+"px)"},Vr=qr,zr=function(e,t){var r=qr(e);return r?t?r+" scale("+Wr.drop+")":r:null},$r=.33,Yr=.55,Jr=Yr-$r,Xr=function(e){var t=e.getState,r=e.dispatch;return function(e){return function(n){if("DROP"===n.type){var i=t(),o=n.payload.reason;if("COLLECTING"!==i.phase){if("IDLE"!==i.phase){"DROP_PENDING"===i.phase&&i.isWaiting&&s(!1),"DRAGGING"!==i.phase&&"DROP_PENDING"!==i.phase&&s(!1);var a=i.critical,u=i.dimensions,c=u.draggables[i.critical.draggable.id],d=function(e){var t=e.draggables,r=e.reason,n=e.lastImpact,i=e.home,o=e.viewport,a=e.onLiftImpact;return n.at&&"DROP"===r?"REORDER"===n.at.type?{impact:n,didDropInsideDroppable:!0}:{impact:l({},n,{displaced:Et}),didDropInsideDroppable:!0}:{impact:mr({draggables:t,impact:a,destination:i,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1}}({reason:o,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),p=d.impact,f=d.didDropInsideDroppable,g=f?yt(p):null,v=f?xt(p):null,m={index:a.draggable.index,droppableId:a.droppable.id},b={draggableId:c.descriptor.id,type:c.descriptor.type,source:m,reason:o,mode:i.movementMode,destination:g,combine:v},h=function(e){var t=e.impact,r=e.draggable,n=e.dimensions,i=e.viewport,o=e.afterCritical,a=n.draggables,l=n.droppables,u=or(t),c=u?l[u]:null,s=l[r.descriptor.droppableId],d=br({impact:t,draggable:r,draggables:a,afterCritical:o,droppable:c||s,viewport:i});return ke(d,r.client.borderBox.center)}({impact:p,draggable:c,dimensions:u,viewport:i.viewport,afterCritical:i.afterCritical}),y={critical:i.critical,afterCritical:i.afterCritical,result:b,impact:p};if(!We(i.current.client.offset,h)||Boolean(b.combine)){var x=function(e){var t=e.current,r=e.destination,n=e.reason,i=qe(t,r);if(i<=0)return $r;if(i>=1500)return Yr;var o=$r+Jr*(i/1500);return Number(("CANCEL"===n?.6*o:o).toFixed(2))}({current:i.current.client.offset,destination:h,reason:o});r(function(e){return{type:"DROP_ANIMATE",payload:e}}({newHomeClientOffset:h,dropDuration:x,completed:y}))}else r(Gr({completed:y}))}}else r(function(e){return{type:"DROP_PENDING",payload:e}}({reason:o}))}else e(n)}}},Kr=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];t=i,r||(r=requestAnimationFrame((function(){r=null,e.apply(void 0,t)})))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n},Qr=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function Zr(e){var t=e.onWindowScroll;var r=Kr((function(){t(Qr())})),n=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}(r),i=a;function o(){return i!==a}return{start:function(){o()&&s(!1),i=u(window,[n])},stop:function(){o()||s(!1),r.cancel(),i(),i=a},isActive:o}}var en=function(e){var t=Zr({onWindowScroll:function(t){e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return function(e){return function(r){t.isActive()||"INITIAL_PUBLISH"!==r.type||t.start(),t.isActive()&&function(e){return"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type}(r)&&t.stop(),e(r)}}},tn=function(){var e=[];return{add:function(t){var r=setTimeout((function(){return function(t){var r=dt(e,(function(e){return e.timerId===t}));-1===r&&s(!1),e.splice(r,1)[0].callback()}(r)})),n={timerId:r,callback:t};e.push(n)},flush:function(){if(e.length){var t=[].concat(e);e.length=0,t.forEach((function(e){clearTimeout(e.timerId),e.callback()}))}}}},rn=function(e,t){t()},nn=function(e,t){return{draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t}},on=function(e,t,r,n){if(e){var i=function(e){var t=!1,r=!1,n=setTimeout((function(){r=!0})),i=function(i){t||r||(t=!0,e(i),clearTimeout(n))};return i.wasCalled=function(){return t},i}(r);e(t,{announce:i}),i.wasCalled()||r(n(t))}else r(n(t))},an=function(e,t){var r=function(e,t){var r=tn(),n=null,i=function(r){n||s(!1),n=null,rn(0,(function(){return on(e().onDragEnd,r,t,y)}))};return{beforeCapture:function(t,r){n&&s(!1),rn(0,(function(){var n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})}))},beforeStart:function(t,r){n&&s(!1),rn(0,(function(){var n=e().onBeforeDragStart;n&&n(nn(t,r))}))},start:function(i,o){n&&s(!1);var a=nn(i,o);n={mode:o,lastCritical:i,lastLocation:a.source,lastCombine:null},r.add((function(){rn(0,(function(){return on(e().onDragStart,a,t,b)}))}))},update:function(i,o){var a=yt(o),u=xt(o);n||s(!1);var c=!function(e,t){if(e===t)return!0;var r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n}(i,n.lastCritical);c&&(n.lastCritical=i);var d,p,f=(d=n.lastLocation,p=a,!(null==d&&null==p||null!=d&&null!=p&&d.droppableId===p.droppableId&&d.index===p.index));f&&(n.lastLocation=a);var g=!function(e,t){return null==e&&null==t||null!=e&&null!=t&&(e.draggableId===t.draggableId&&e.droppableId===t.droppableId)}(n.lastCombine,u);if(g&&(n.lastCombine=u),c||f||g){var v=l({},nn(i,n.mode),{combine:u,destination:a});r.add((function(){rn(0,(function(){return on(e().onDragUpdate,v,t,h)}))}))}},flush:function(){n||s(!1),r.flush()},drop:i,abort:function(){if(n){var e=l({},nn(n.lastCritical,n.mode),{combine:null,destination:null,reason:"CANCEL"});i(e)}}}}(e,t);return function(e){return function(t){return function(n){if("BEFORE_INITIAL_CAPTURE"!==n.type){if("INITIAL_PUBLISH"===n.type){var i=n.payload.critical;return r.beforeStart(i,n.payload.movementMode),t(n),void r.start(i,n.payload.movementMode)}if("DROP_COMPLETE"===n.type){var o=n.payload.completed.result;return r.flush(),t(n),void r.drop(o)}if(t(n),"FLUSH"!==n.type){var a=e.getState();"DRAGGING"===a.phase&&r.update(a.critical,a.impact)}else r.abort()}else r.beforeCapture(n.payload.draggableId,n.payload.movementMode)}}}},ln=function(e){return function(t){return function(r){if("DROP_ANIMATION_FINISHED"===r.type){var n=e.getState();"DROP_ANIMATING"!==n.phase&&s(!1),e.dispatch(Gr({completed:n.completed}))}else t(r)}}},un=function(e){var t=null,r=null;return function(n){return function(i){if("FLUSH"!==i.type&&"DROP_COMPLETE"!==i.type&&"DROP_ANIMATION_FINISHED"!==i.type||(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(i),"DROP_ANIMATE"===i.type){var o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};r=requestAnimationFrame((function(){r=null,t=u(window,[o])}))}}}},cn=function(e){return function(t){return function(r){if(t(r),"PUBLISH_WHILE_DRAGGING"===r.type){var n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(_r({reason:n.reason})))}}}},sn=R,dn=function(e){var t,r=e.dimensionMarshal,n=e.focusMarshal,i=e.styleMarshal,o=e.getResponders,a=e.announce,l=e.autoScroller;return E(Cr,sn(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),n=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},i={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},o=t.map((function(e){return e(i)}));return A({},r,{dispatch:n=R.apply(void 0,o)(r.dispatch)})}}}((t=i,function(){return function(e){return function(r){"INITIAL_PUBLISH"===r.type&&t.dragging(),"DROP_ANIMATE"===r.type&&t.dropping(r.payload.completed.result.reason),"FLUSH"!==r.type&&"DROP_COMPLETE"!==r.type||t.resting(),e(r)}}}),function(e){return function(){return function(t){return function(r){"DROP_COMPLETE"!==r.type&&"FLUSH"!==r.type&&"DROP_ANIMATE"!==r.type||e.stopPublishing(),t(r)}}}}(r),function(e){return function(t){var r=t.getState,n=t.dispatch;return function(t){return function(i){if("LIFT"===i.type){var o=i.payload,a=o.id,l=o.clientSelection,u=o.movementMode,c=r();"DROP_ANIMATING"===c.phase&&n(Gr({completed:c.completed})),"IDLE"!==r().phase&&s(!1),n({type:"FLUSH",payload:null}),n({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:a,movementMode:u}});var d={draggableId:a,scrollOptions:{shouldPublishImmediately:"SNAP"===u}},p=e.startPublishing(d),f=p.critical,g=p.dimensions,v=p.viewport;n(function(e){return{type:"INITIAL_PUBLISH",payload:e}}({critical:f,dimensions:g,clientSelection:l,movementMode:u,viewport:v}))}else t(i)}}}}(r),Xr,ln,un,cn,function(e){return function(t){return function(r){return function(n){if(function(e){return"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type}(n))return e.stop(),void r(n);if("INITIAL_PUBLISH"===n.type){r(n);var i=t.getState();return"DRAGGING"!==i.phase&&s(!1),void e.start(i)}r(n),e.scroll(t.getState())}}}}(l),en,function(e){var t=!1;return function(){return function(r){return function(n){if("INITIAL_PUBLISH"===n.type)return t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),void e.tryRestoreFocusRecorded();if(r(n),t){if("FLUSH"===n.type)return t=!1,void e.tryRestoreFocusRecorded();if("DROP_COMPLETE"===n.type){t=!1;var i=n.payload.completed.result;i.combine&&e.tryShiftRecord(i.draggableId,i.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}}(n),an(o,a))))};var pn=function(e){var t=e.scrollHeight,r=e.scrollWidth,n=e.height,i=e.width,o=ke({x:r,y:t},{x:i,y:n});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},fn=function(){var e=document.documentElement;return e||s(!1),e},gn=function(){var e=fn();return pn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},vn=function(e){var t=e.critical,r=e.scrollOptions,n=e.registry,i=function(){var e=Qr(),t=gn(),r=e.y,n=e.x,i=fn(),o=i.clientWidth,a=i.clientHeight;return{frame:$e({top:r,left:n,right:n+o,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:Fe,displacement:Fe}}}}(),o=i.scroll.current,a=t.droppable,l=n.droppable.getAllByType(a.type).map((function(e){return e.callbacks.getDimensionAndWatchScroll(o,r)})),u=n.draggable.getAllByType(t.draggable.type).map((function(e){return e.getDimension(o)}));return{dimensions:{draggables:vt(u),droppables:gt(l)},critical:t,viewport:i}};function mn(e,t,r){return r.descriptor.id!==t.id&&(r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode)}var bn,hn,yn=function(e,t){var r=null,n=function(e){var t=e.registry,r=e.callbacks,n={additions:{},removals:{},modified:{}},i=null,o=function(){i||(r.collectionStarting(),i=requestAnimationFrame((function(){i=null;var e=n,o=e.additions,a=e.removals,l=e.modified,u=Object.keys(o).map((function(e){return t.draggable.getById(e).getDimension(Fe)})).sort((function(e,t){return e.descriptor.index-t.descriptor.index})),c=Object.keys(l).map((function(e){return{droppableId:e,scroll:t.droppable.getById(e).callbacks.getScrollWhileDragging()}})),s={additions:u,removals:Object.keys(a),modified:c};n={additions:{},removals:{},modified:{}},r.publish(s)})))};return{add:function(e){var t=e.descriptor.id;n.additions[t]=e,n.modified[e.descriptor.droppableId]=!0,n.removals[t]&&delete n.removals[t],o()},remove:function(e){var t=e.descriptor;n.removals[t.id]=!0,n.modified[t.droppableId]=!0,n.additions[t.id]&&delete n.additions[t.id],o()},stop:function(){i&&(cancelAnimationFrame(i),i=null,n={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),i=function(t){r||s(!1);var i=r.critical.draggable;"ADDITION"===t.type&&mn(e,i,t.value)&&n.add(t.value),"REMOVAL"===t.type&&mn(e,i,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:function(n,i){e.droppable.exists(n)||s(!1),r&&t.updateDroppableIsEnabled({id:n,isEnabled:i})},updateDroppableIsCombineEnabled:function(n,i){r&&(e.droppable.exists(n)||s(!1),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:i}))},scrollDroppable:function(t,n){r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:function(n,i){r&&(e.droppable.exists(n)||s(!1),t.updateDroppableScroll({id:n,newScroll:i}))},startPublishing:function(t){r&&s(!1);var n=e.draggable.getById(t.draggableId),o=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:o.descriptor},l=e.subscribe(i);return r={critical:a,unsubscribe:l},vn({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:function(){if(r){n.stop();var t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach((function(e){return e.callbacks.dragStopped()})),r.unsubscribe(),r=null}}}},xn=function(e,t){return"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&(e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason)},In=function(e){window.scrollBy(e.x,e.y)},Dn=ct((function(e){return mt(e).filter((function(e){return!!e.isEnabled&&!!e.frame}))})),wn=function(e){var t=e.center,r=e.destination,n=e.droppables;if(r){var i=n[r];return i.frame?i:null}return function(e,t){return pt(Dn(t),(function(t){return t.frame||s(!1),ur(t.frame.pageMarginBox)(e)}))}(t,n)},En=.25,Cn=.05,Sn=28,Pn=function(e){return Math.pow(e,2)},On={stopDampeningAt:1200,accelerateAt:360},An=function(e){var t=e.startOfRange,r=e.endOfRange,n=e.current,i=r-t;return 0===i?0:(n-t)/i},Rn=On.accelerateAt,Nn=On.stopDampeningAt,Bn=function(e){var t=e.distanceToEdge,r=e.thresholds,n=e.dragStartTime,i=e.shouldUseTimeDampening,o=function(e,t){if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return Sn;if(e===t.startScrollingFrom)return 1;var r=An({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),n=Sn*Pn(1-r);return Math.ceil(n)}(t,r);return 0===o?0:i?Math.max(function(e,t){var r=t,n=Nn,i=Date.now()-r;if(i>=Nn)return e;if(i<Rn)return 1;var o=An({startOfRange:Rn,endOfRange:n,current:i}),a=e*Pn(o);return Math.ceil(a)}(o,n),1):o},Tn=function(e){var t=e.container,r=e.distanceToEdges,n=e.dragStartTime,i=e.axis,o=e.shouldUseTimeDampening,a=function(e,t){return{startScrollingFrom:e[t.size]*En,maxScrollValueAt:e[t.size]*Cn}}(t,i);return r[i.end]<r[i.start]?Bn({distanceToEdge:r[i.end],thresholds:a,dragStartTime:n,shouldUseTimeDampening:o}):-1*Bn({distanceToEdge:r[i.start],thresholds:a,dragStartTime:n,shouldUseTimeDampening:o})},Mn=ze((function(e){return 0===e?0:e})),Ln=function(e){var t=e.dragStartTime,r=e.container,n=e.subject,i=e.center,o=e.shouldUseTimeDampening,a={top:i.y-r.top,right:r.right-i.x,bottom:r.bottom-i.y,left:i.x-r.left},l=Tn({container:r,distanceToEdges:a,dragStartTime:t,axis:At,shouldUseTimeDampening:o}),u=Tn({container:r,distanceToEdges:a,dragStartTime:t,axis:Rt,shouldUseTimeDampening:o}),c=Mn({x:u,y:l});if(We(c,Fe))return null;var s=function(e){var t=e.container,r=e.subject,n=e.proposedScroll,i=r.height>t.height,o=r.width>t.width;return o||i?o&&i?null:{x:o?0:n.x,y:i?0:n.y}:n}({container:r,subject:n,proposedScroll:c});return s?We(s,Fe)?null:s:null},Gn=ze((function(e){return 0===e?0:e>0?1:-1})),_n=(bn=function(e,t){return e<0?e:e>t?e-t:0},function(e){var t=e.current,r=e.max,n=e.change,i=je(t,n),o={x:bn(i.x,r.x),y:bn(i.y,r.y)};return We(o,Fe)?null:o}),Fn=function(e){var t=e.max,r=e.current,n=e.change,i={x:Math.max(r.x,t.x),y:Math.max(r.y,t.y)},o=Gn(n),a=_n({max:i,current:r,change:o});return!a||(0!==o.x&&0===a.x||0!==o.y&&0===a.y)},jn=function(e,t){return Fn({current:e.scroll.current,max:e.scroll.max,change:t})},kn=function(e,t){var r=e.frame;return!!r&&Fn({current:r.scroll.current,max:r.scroll.max,change:t})},Wn=function(e){var t=e.state,r=e.dragStartTime,n=e.shouldUseTimeDampening,i=e.scrollWindow,o=e.scrollDroppable,a=t.current.page.borderBoxCenter,l=t.dimensions.draggables[t.critical.draggable.id].page.marginBox;if(t.isWindowScrollAllowed){var u=function(e){var t=e.viewport,r=e.subject,n=e.center,i=e.dragStartTime,o=e.shouldUseTimeDampening,a=Ln({dragStartTime:i,container:t.frame,subject:r,center:n,shouldUseTimeDampening:o});return a&&jn(t,a)?a:null}({dragStartTime:r,viewport:t.viewport,subject:l,center:a,shouldUseTimeDampening:n});if(u)return void i(u)}var c=wn({center:a,destination:or(t.impact),droppables:t.dimensions.droppables});if(c){var s=function(e){var t=e.droppable,r=e.subject,n=e.center,i=e.dragStartTime,o=e.shouldUseTimeDampening,a=t.frame;if(!a)return null;var l=Ln({dragStartTime:i,container:a.pageMarginBox,subject:r,center:n,shouldUseTimeDampening:o});return l&&kn(t,l)?l:null}({dragStartTime:r,droppable:c,subject:l,center:a,shouldUseTimeDampening:n});s&&o(c.descriptor.id,s)}},Un=function(e){var t=e.move,r=e.scrollDroppable,n=e.scrollWindow,i=function(e,t){if(!kn(e,t))return t;var n=function(e,t){var r=e.frame;return r&&kn(e,t)?_n({current:r.scroll.current,max:r.scroll.max,change:t}):null}(e,t);if(!n)return r(e.descriptor.id,t),null;var i=ke(t,n);return r(e.descriptor.id,i),ke(t,i)},o=function(e,t,r){if(!e)return r;if(!jn(t,r))return r;var i=function(e,t){if(!jn(e,t))return null;var r=e.scroll.max,n=e.scroll.current;return _n({current:n,max:r,change:t})}(t,r);if(!i)return n(r),null;var o=ke(r,i);return n(o),ke(r,o)};return function(e){var r=e.scrollJumpRequest;if(r){var n=or(e.impact);n||s(!1);var a=i(e.dimensions.droppables[n],r);if(a){var l=e.viewport,u=o(e.isWindowScrollAllowed,l,a);u&&function(e,r){var n=je(e.current.client.selection,r);t({client:n})}(e,u)}}}},Hn=function(e){var t=e.scrollDroppable,r=e.scrollWindow,n=e.move,i=function(e){var t=e.scrollWindow,r=e.scrollDroppable,n=Kr(t),i=Kr(r),o=null,a=function(e){o||s(!1);var t=o,r=t.shouldUseTimeDampening,a=t.dragStartTime;Wn({state:e,scrollWindow:n,scrollDroppable:i,dragStartTime:a,shouldUseTimeDampening:r})};return{start:function(e){o&&s(!1);var t=Date.now(),r=!1,n=function(){r=!0};Wn({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:n,scrollDroppable:n}),o={dragStartTime:t,shouldUseTimeDampening:r},r&&a(e)},stop:function(){o&&(n.cancel(),i.cancel(),o=null)},scroll:a}}({scrollWindow:r,scrollDroppable:t}),o=Un({move:n,scrollWindow:r,scrollDroppable:t});return{scroll:function(e){"DRAGGING"===e.phase&&("FLUID"!==e.movementMode?e.scrollJumpRequest&&o(e):i.scroll(e))},start:i.start,stop:i.stop}},qn={base:hn="data-rbd-drag-handle",draggableId:hn+"-draggable-id",contextId:hn+"-context-id"},Vn=function(){var e="data-rbd-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),zn=function(){var e="data-rbd-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),$n={contextId:"data-rbd-scroll-container-context-id"},Yn=function(e,t){return e.map((function(e){var r=e.styles[t];return r?e.selector+" { "+r+" }":""})).join(" ")},Jn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect,Xn=function(){var e=document.querySelector("head");return e||s(!1),e},Kn=function(e){var t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function Qn(e,r){var n=Ge((function(){return function(e){var t,r,n,i=(t=e,function(e){return"["+e+'="'+t+'"]'}),o=(r="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ",{selector:i(qn.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:r,dragging:"pointer-events: none;",dropAnimating:r}}),a=[(n="\n      transition: "+Hr.outOfTheWay+";\n    ",{selector:i(Vn.contextId),styles:{dragging:n,dropAnimating:n,userCancel:n}}),o,{selector:i(zn.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Yn(a,"always"),resting:Yn(a,"resting"),dragging:Yn(a,"dragging"),dropAnimating:Yn(a,"dropAnimating"),userCancel:Yn(a,"userCancel")}}(e)}),[e]),i=t.useRef(null),o=t.useRef(null),a=_e(ct((function(e){var t=o.current;t||s(!1),t.textContent=e})),[]),l=_e((function(e){var t=i.current;t||s(!1),t.textContent=e}),[]);Jn((function(){(i.current||o.current)&&s(!1);var t=Kn(r),u=Kn(r);return i.current=t,o.current=u,t.setAttribute("data-rbd-always",e),u.setAttribute("data-rbd-dynamic",e),Xn().appendChild(t),Xn().appendChild(u),l(n.always),a(n.resting),function(){var e=function(e){var t=e.current;t||s(!1),Xn().removeChild(t),e.current=null};e(i),e(o)}}),[r,l,a,n.always,n.resting,e]);var u=_e((function(){return a(n.dragging)}),[a,n.dragging]),c=_e((function(e){a("DROP"!==e?n.userCancel:n.dropAnimating)}),[a,n.dropAnimating,n.userCancel]),d=_e((function(){o.current&&a(n.resting)}),[a,n.resting]);return Ge((function(){return{dragging:u,dropping:c,resting:d}}),[u,c,d])}var Zn=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function ei(e){return e instanceof Zn(e).HTMLElement}function ti(e){var r=t.useRef({}),n=t.useRef(null),i=t.useRef(null),o=t.useRef(!1),a=_e((function(e,t){var n={id:e,focus:t};return r.current[e]=n,function(){var t=r.current;t[e]!==n&&delete t[e]}}),[]),l=_e((function(t){var r=function(e,t){var r="["+qn.contextId+'="'+e+'"]',n=ft(document.querySelectorAll(r));if(!n.length)return null;var i=pt(n,(function(e){return e.getAttribute(qn.draggableId)===t}));return i&&ei(i)?i:null}(e,t);r&&r!==document.activeElement&&r.focus()}),[e]),u=_e((function(e,t){n.current===e&&(n.current=t)}),[]),c=_e((function(){i.current||o.current&&(i.current=requestAnimationFrame((function(){i.current=null;var e=n.current;e&&l(e)})))}),[l]),s=_e((function(e){n.current=null;var t=document.activeElement;t&&t.getAttribute(qn.draggableId)===e&&(n.current=e)}),[]);return Jn((function(){return o.current=!0,function(){o.current=!1;var e=i.current;e&&cancelAnimationFrame(e)}}),[]),Ge((function(){return{register:a,tryRecordFocus:s,tryRestoreFocusRecorded:c,tryShiftRecord:u}}),[a,s,c,u])}function ri(){var e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach((function(t){return t(e)}))}function n(t){return e.draggables[t]||null}function i(t){return e.droppables[t]||null}return{draggable:{register:function(t){e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:function(t,r){var n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:function(t){var i=t.descriptor.id,o=n(i);o&&t.uniqueId===o.uniqueId&&(delete e.draggables[i],r({type:"REMOVAL",value:t}))},getById:function(e){var t=n(e);return t||s(!1),t},findById:n,exists:function(e){return Boolean(n(e))},getAllByType:function(t){return st(e.draggables).filter((function(e){return e.descriptor.type===t}))}},droppable:{register:function(t){e.droppables[t.descriptor.id]=t},unregister:function(t){var r=i(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){var t=i(e);return t||s(!1),t},findById:i,exists:function(e){return Boolean(i(e))},getAllByType:function(t){return st(e.droppables).filter((function(e){return e.descriptor.type===t}))}},subscribe:function(e){return t.push(e),function(){var r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var ni=n.createContext(null),ii=function(){var e=document.body;return e||s(!1),e},oi={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"};var ai=0,li={separator:"::"};function ui(e,t){return void 0===t&&(t=li),Ge((function(){return""+e+t.separator+ai++}),[t.separator,e])}var ci=n.createContext(null);function si(e){var r=t.useRef(e);return t.useEffect((function(){r.current=e})),r}var di,pi=((di={})[13]=!0,di[9]=!0,di),fi=function(e){pi[e.keyCode]&&e.preventDefault()},gi=function(){var e="visibilitychange";return"undefined"==typeof document?e:pt([e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],(function(e){return"on"+e in document}))||e}();var vi,mi={type:"IDLE"};function bi(e){var t=e.cancel,r=e.completed,n=e.getPhase,i=e.setPhase;return[{eventName:"mousemove",fn:function(e){var t=e.button,r=e.clientX,o=e.clientY;if(0===t){var a={x:r,y:o},l=n();if("DRAGGING"===l.type)return e.preventDefault(),void l.actions.move(a);"PENDING"!==l.type&&s(!1);var u=l.point;if(c=u,d=a,Math.abs(d.x-c.x)>=5||Math.abs(d.y-c.y)>=5){var c,d;e.preventDefault();var p=l.actions.fluidLift(a);i({type:"DRAGGING",actions:p})}}}},{eventName:"mouseup",fn:function(e){var i=n();"DRAGGING"===i.type?(e.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),r()):t()}},{eventName:"mousedown",fn:function(e){"DRAGGING"===n().type&&e.preventDefault(),t()}},{eventName:"keydown",fn:function(e){if("PENDING"!==n().type)return 27===e.keyCode?(e.preventDefault(),void t()):void fi(e);t()}},{eventName:"resize",fn:t},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){"PENDING"===n().type&&t()}},{eventName:"webkitmouseforcedown",fn:function(e){var r=n();"IDLE"===r.type&&s(!1),r.actions.shouldRespectForcePress()?t():e.preventDefault()}},{eventName:gi,fn:t}]}function hi(e){var r=t.useRef(mi),n=t.useRef(a),i=Ge((function(){return{eventName:"mousedown",fn:function(t){if(!t.defaultPrevented&&0===t.button&&!(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)){var r=e.findClosestDraggableId(t);if(r){var i=e.tryGetLock(r,c,{sourceEvent:t});if(i){t.preventDefault();var o={x:t.clientX,y:t.clientY};n.current(),f(i,o)}}}}}}),[e]),o=Ge((function(){return{eventName:"webkitmouseforcewillbegin",fn:function(t){if(!t.defaultPrevented){var r=e.findClosestDraggableId(t);if(r){var n=e.findOptionsForDraggable(r);n&&(n.shouldRespectForcePress||e.canGetLock(r)&&t.preventDefault())}}}}}),[e]),l=_e((function(){n.current=u(window,[o,i],{passive:!1,capture:!0})}),[o,i]),c=_e((function(){"IDLE"!==r.current.type&&(r.current=mi,n.current(),l())}),[l]),d=_e((function(){var e=r.current;c(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[c]),p=_e((function(){var e=bi({cancel:d,completed:c,getPhase:function(){return r.current},setPhase:function(e){r.current=e}});n.current=u(window,e,{capture:!0,passive:!1})}),[d,c]),f=_e((function(e,t){"IDLE"!==r.current.type&&s(!1),r.current={type:"PENDING",point:t,actions:e},p()}),[p]);Jn((function(){return l(),function(){n.current()}}),[l])}function yi(){}var xi=((vi={})[34]=!0,vi[33]=!0,vi[36]=!0,vi[35]=!0,vi);function Ii(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:function(n){return 27===n.keyCode?(n.preventDefault(),void r()):32===n.keyCode?(n.preventDefault(),t(),void e.drop()):40===n.keyCode?(n.preventDefault(),void e.moveDown()):38===n.keyCode?(n.preventDefault(),void e.moveUp()):39===n.keyCode?(n.preventDefault(),void e.moveRight()):37===n.keyCode?(n.preventDefault(),void e.moveLeft()):void(xi[n.keyCode]?n.preventDefault():fi(n))}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:gi,fn:r}]}function Di(e){var r=t.useRef(yi),n=Ge((function(){return{eventName:"keydown",fn:function(t){if(!t.defaultPrevented&&32===t.keyCode){var n=e.findClosestDraggableId(t);if(n){var o=e.tryGetLock(n,c,{sourceEvent:t});if(o){t.preventDefault();var a=!0,l=o.snapLift();r.current(),r.current=u(window,Ii(l,c),{capture:!0,passive:!1})}}}function c(){a||s(!1),a=!1,r.current(),i()}}}}),[e]),i=_e((function(){r.current=u(window,[n],{passive:!1,capture:!0})}),[n]);Jn((function(){return i(),function(){r.current()}}),[i])}var wi={type:"IDLE"};function Ei(e){var r=t.useRef(wi),n=t.useRef(a),i=_e((function(){return r.current}),[]),o=_e((function(e){r.current=e}),[]),l=Ge((function(){return{eventName:"touchstart",fn:function(t){if(!t.defaultPrevented){var r=e.findClosestDraggableId(t);if(r){var i=e.tryGetLock(r,d,{sourceEvent:t});if(i){var o=t.touches[0],a={x:o.clientX,y:o.clientY};n.current(),v(i,a)}}}}}}),[e]),c=_e((function(){n.current=u(window,[l],{capture:!0,passive:!1})}),[l]),d=_e((function(){var e=r.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),o(wi),n.current(),c())}),[c,o]),p=_e((function(){var e=r.current;d(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[d]),f=_e((function(){var e={capture:!0,passive:!1},t={cancel:p,completed:d,getPhase:i},r=u(window,function(e){var t=e.cancel,r=e.completed,n=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(e){var r=n();if("DRAGGING"===r.type){r.hasMoved=!0;var i=e.touches[0],o={x:i.clientX,y:i.clientY};e.preventDefault(),r.actions.move(o)}else t()}},{eventName:"touchend",fn:function(e){var i=n();"DRAGGING"===i.type?(e.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),r()):t()}},{eventName:"touchcancel",fn:function(e){"DRAGGING"===n().type?(e.preventDefault(),t()):t()}},{eventName:"touchforcechange",fn:function(e){var r=n();"IDLE"===r.type&&s(!1);var i=e.touches[0];if(i&&i.force>=.15){var o=r.actions.shouldRespectForcePress();if("PENDING"!==r.type)return o?r.hasMoved?void e.preventDefault():void t():void e.preventDefault();o&&t()}}},{eventName:gi,fn:t}]}(t),e),o=u(window,function(e){var t=e.cancel,r=e.getPhase;return[{eventName:"orientationchange",fn:t},{eventName:"resize",fn:t},{eventName:"contextmenu",fn:function(e){e.preventDefault()}},{eventName:"keydown",fn:function(e){"DRAGGING"===r().type?(27===e.keyCode&&e.preventDefault(),t()):t()}},{eventName:gi,fn:t}]}(t),e);n.current=function(){r(),o()}}),[p,i,d]),g=_e((function(){var e=i();"PENDING"!==e.type&&s(!1);var t=e.actions.fluidLift(e.point);o({type:"DRAGGING",actions:t,hasMoved:!1})}),[i,o]),v=_e((function(e,t){"IDLE"!==i().type&&s(!1);var r=setTimeout(g,120);o({type:"PENDING",point:t,actions:e,longPressTimerId:r}),f()}),[f,i,o,g]);Jn((function(){return c(),function(){n.current();var e=i();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),o(wi))}}),[i,c,o]),Jn((function(){return u(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}])}),[])}var Ci={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function Si(e,t){var r=t.target;return!!ei(r)&&function e(t,r){if(null==r)return!1;if(Boolean(Ci[r.tagName.toLowerCase()]))return!0;var n=r.getAttribute("contenteditable");return"true"===n||""===n||r!==t&&e(t,r.parentElement)}(e,r)}var Pi=function(e){return $e(e.getBoundingClientRect()).center};var Oi="undefined"==typeof document?"matches":pt(["matches","msMatchesSelector","webkitMatchesSelector"],(function(e){return e in Element.prototype}))||"matches";function Ai(e,t){return e.closest?e.closest(t):function e(t,r){return null==t?null:t[Oi](r)?t:e(t.parentElement,r)}(e,t)}function Ri(e,t){var r,n=t.target;if(!((r=n)instanceof Zn(r).Element))return null;var i=Ai(n,function(e){return"["+qn.contextId+'="'+e+'"]'}(e));return i&&ei(i)?i:null}function Ni(e){e.preventDefault()}function Bi(e){var t=e.expected,r=e.phase,n=e.isLockActive;e.shouldWarn;return!!n()&&t===r}function Ti(e){var t=e.lockAPI,r=e.store,n=e.registry,i=e.draggableId;if(t.isClaimed())return!1;var o=n.draggable.findById(i);return!!o&&(!!o.options.isEnabled&&!!xn(r.getState(),i))}function Mi(e){var t=e.lockAPI,r=e.contextId,n=e.store,i=e.registry,o=e.draggableId,c=e.forceSensorStop,d=e.sourceEvent;if(!Ti({lockAPI:t,store:n,registry:i,draggableId:o}))return null;var p=i.draggable.getById(o),f=function(e,t){var r="["+Vn.contextId+'="'+e+'"]',n=pt(ft(document.querySelectorAll(r)),(function(e){return e.getAttribute(Vn.id)===t}));return n&&ei(n)?n:null}(r,p.descriptor.id);if(!f)return null;if(d&&!p.options.canDragInteractiveElements&&Si(f,d))return null;var g=t.claim(c||a),v="PRE_DRAG";function m(){return p.options.shouldRespectForcePress}function b(){return t.isActive(g)}var h=function(e,t){Bi({expected:e,phase:v,isLockActive:b,shouldWarn:!0})&&n.dispatch(t())}.bind(null,"DRAGGING");function y(e){function r(){t.release(),v="COMPLETED"}function i(t,i){if(void 0===i&&(i={shouldBlockNextClick:!1}),e.cleanup(),i.shouldBlockNextClick){var o=u(window,[{eventName:"click",fn:Ni,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(o)}r(),n.dispatch(_r({reason:t}))}return"PRE_DRAG"!==v&&(r(),"PRE_DRAG"!==v&&s(!1)),n.dispatch(function(e){return{type:"LIFT",payload:e}}(e.liftActionArgs)),v="DRAGGING",l({isActive:function(){return Bi({expected:"DRAGGING",phase:v,isLockActive:b,shouldWarn:!1})},shouldRespectForcePress:m,drop:function(e){return i("DROP",e)},cancel:function(e){return i("CANCEL",e)}},e.actions)}return{isActive:function(){return Bi({expected:"PRE_DRAG",phase:v,isLockActive:b,shouldWarn:!1})},shouldRespectForcePress:m,fluidLift:function(e){var t=Kr((function(e){h((function(){return Nr({client:e})}))}));return l({},y({liftActionArgs:{id:o,clientSelection:e,movementMode:"FLUID"},cleanup:function(){return t.cancel()},actions:{move:t}}),{move:t})},snapLift:function(){var e={moveUp:function(){return h(Br)},moveRight:function(){return h(Mr)},moveDown:function(){return h(Tr)},moveLeft:function(){return h(Lr)}};return y({liftActionArgs:{id:o,clientSelection:Pi(f),movementMode:"SNAP"},cleanup:a,actions:e})},abort:function(){Bi({expected:"PRE_DRAG",phase:v,isLockActive:b,shouldWarn:!0})&&t.release()}}}var Li=[hi,Di,Ei];function Gi(e){var r=e.contextId,n=e.store,i=e.registry,o=e.customSensors,a=e.enableDefaultSensors,l=[].concat(a?Li:[],o||[]),u=t.useState((function(){return function(){var e=null;function t(){e||s(!1),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&s(!1);var r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()}))[0],c=_e((function(e,t){e.isDragging&&!t.isDragging&&u.tryAbandon()}),[u]);Jn((function(){var e=n.getState();return n.subscribe((function(){var t=n.getState();c(e,t),e=t}))}),[u,n,c]),Jn((function(){return u.tryAbandon}),[u.tryAbandon]);for(var d=_e((function(e){return Ti({lockAPI:u,registry:i,store:n,draggableId:e})}),[u,i,n]),p=_e((function(e,t,o){return Mi({lockAPI:u,registry:i,contextId:r,store:n,draggableId:e,forceSensorStop:t,sourceEvent:o&&o.sourceEvent?o.sourceEvent:null})}),[r,u,i,n]),f=_e((function(e){return function(e,t){var r=Ri(e,t);return r?r.getAttribute(qn.draggableId):null}(r,e)}),[r]),g=_e((function(e){var t=i.draggable.findById(e);return t?t.options:null}),[i.draggable]),v=_e((function(){u.isClaimed()&&(u.tryAbandon(),"IDLE"!==n.getState().phase&&n.dispatch({type:"FLUSH",payload:null}))}),[u,n]),m=_e(u.isClaimed,[u]),b=Ge((function(){return{canGetLock:d,tryGetLock:p,findClosestDraggableId:f,findOptionsForDraggable:g,tryReleaseLock:v,isLockClaimed:m}}),[d,p,f,g,v,m]),h=0;h<l.length;h++)l[h](b)}function _i(e){return e.current||s(!1),e.current}function Fi(e){var r=e.contextId,i=e.setCallbacks,o=e.sensors,a=e.nonce,u=e.dragHandleUsageInstructions,c=t.useRef(null),s=si(e),d=_e((function(){return function(e){return{onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}}(s.current)}),[s]),p=function(e){var r=Ge((function(){return function(e){return"rbd-announcement-"+e}(e)}),[e]),n=t.useRef(null);return t.useEffect((function(){var e=document.createElement("div");return n.current=e,e.id=r,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),l(e.style,oi),ii().appendChild(e),function(){setTimeout((function(){var t=ii();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)}))}}),[r]),_e((function(e){var t=n.current;t&&(t.textContent=e)}),[])}(r),f=function(e){var r=e.contextId,n=e.text,i=ui("hidden-text",{separator:"-"}),o=Ge((function(){return"rbd-hidden-text-"+(e={contextId:r,uniqueId:i}).contextId+"-"+e.uniqueId;var e}),[i,r]);return t.useEffect((function(){var e=document.createElement("div");return e.id=o,e.textContent=n,e.style.display="none",ii().appendChild(e),function(){var t=ii();t.contains(e)&&t.removeChild(e)}}),[o,n]),o}({contextId:r,text:u}),g=Qn(r,a),v=_e((function(e){_i(c).dispatch(e)}),[]),m=Ge((function(){return S({publishWhileDragging:Sr,updateDroppableScroll:Or,updateDroppableIsEnabled:Ar,updateDroppableIsCombineEnabled:Rr,collectionStarting:Pr},v)}),[v]),b=function(){var e=Ge(ri,[]);return t.useEffect((function(){return function(){requestAnimationFrame(e.clean)}}),[e]),e}(),h=Ge((function(){return yn(b,m)}),[b,m]),y=Ge((function(){return Hn(l({scrollWindow:In,scrollDroppable:h.scrollDroppable},S({move:Nr},v)))}),[h.scrollDroppable,v]),x=ti(r),I=Ge((function(){return dn({announce:p,autoScroller:y,dimensionMarshal:h,focusMarshal:x,getResponders:d,styleMarshal:g})}),[p,y,h,x,d,g]);c.current=I;var D=_e((function(){var e=_i(c);"IDLE"!==e.getState().phase&&e.dispatch({type:"FLUSH",payload:null})}),[]),w=_e((function(){var e=_i(c).getState();return e.isDragging||"DROP_ANIMATING"===e.phase}),[]);i(Ge((function(){return{isDragging:w,tryAbort:D}}),[w,D]));var E=_e((function(e){return xn(_i(c).getState(),e)}),[]),C=_e((function(){return lr(_i(c).getState())}),[]),P=Ge((function(){return{marshal:h,focus:x,contextId:r,canLift:E,isMovementAllowed:C,dragHandleUsageInstructionsId:f,registry:b}}),[r,h,f,x,E,C,b]);return Gi({contextId:r,store:I,registry:b,customSensors:o,enableDefaultSensors:!1!==e.enableDefaultSensors}),t.useEffect((function(){return D}),[D]),n.createElement(ci.Provider,{value:P},n.createElement(Y,{context:ni,store:I},e.children))}var ji=0;var ki=function(e){return function(t){return e===t}},Wi=ki("scroll"),Ui=ki("auto"),Hi=function(e,t){return t(e.overflowX)||t(e.overflowY)},qi=function e(t){return null==t?null:t===document.body?null:t===document.documentElement?null:function(e){var t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return Hi(r,Wi)||Hi(r,Ui)}(t)?t:e(t.parentElement)},Vi=function(e){return{x:e.scrollLeft,y:e.scrollTop}},zi=function(e){return{closestScrollable:qi(e),isFixedOnPage:function e(t){return!!t&&("fixed"===window.getComputedStyle(t).position||e(t.parentElement))}(e)}},$i=function(e){var t=e.ref,r=e.descriptor,n=e.env,i=e.windowScroll,o=e.direction,a=e.isDropDisabled,l=e.isCombineEnabled,u=e.shouldClipSubject,c=n.closestScrollable,s=function(e,t){var r=rt(e);if(!t)return r;if(e!==t)return r;var n=r.paddingBox.top-t.scrollTop,i=r.paddingBox.left-t.scrollLeft,o=n+t.scrollHeight,a=i+t.scrollWidth,l=Ye({top:n,right:a,bottom:o,left:i},r.border);return Ke({borderBox:l,margin:r.margin,border:r.border,padding:r.padding})}(t,c),d=et(s,i),p=function(){if(!c)return null;var e=rt(c),t={scrollHeight:c.scrollHeight,scrollWidth:c.scrollWidth};return{client:e,page:et(e,i),scroll:Vi(c),scrollSize:t,shouldClipSubject:u}}();return function(e){var t=e.descriptor,r=e.isEnabled,n=e.isCombineEnabled,i=e.isFixedOnPage,o=e.direction,a=e.client,l=e.page,u=e.closest,c=function(){if(!u)return null;var e=u.scrollSize,t=u.client,r=pn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:u.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:u.shouldClipSubject,scroll:{initial:u.scroll,current:u.scroll,max:r,diff:{value:Fe,displacement:Fe}}}}(),s="vertical"===o?At:Rt;return{descriptor:t,isCombineEnabled:n,isFixedOnPage:i,axis:s,isEnabled:r,client:a,page:l,frame:c,subject:at({page:l,withPlaceholder:null,axis:s,frame:c})}}({descriptor:r,isEnabled:!a,isCombineEnabled:l,isFixedOnPage:n.isFixedOnPage,direction:o,client:s,page:d,closest:p})},Yi={passive:!1},Ji={passive:!0},Xi=function(e){return e.shouldPublishImmediately?Yi:Ji};function Ki(e){var r=t.useContext(e);return r||s(!1),r}var Qi=function(e){return e&&e.env.closestScrollable||null};function Zi(){}var eo={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},to=function(e){var t=e.isAnimatingOpenOnMount,r=e.placeholder,n=e.animate,i=function(e){var t=e.isAnimatingOpenOnMount,r=e.placeholder,n=e.animate;return t?eo:"close"===n?eo:{height:r.client.borderBox.height,width:r.client.borderBox.width,margin:r.client.margin}}({isAnimatingOpenOnMount:t,placeholder:r,animate:n});return{display:r.display,boxSizing:"border-box",width:i.width,height:i.height,marginTop:i.margin.top,marginRight:i.margin.right,marginBottom:i.margin.bottom,marginLeft:i.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==n?Hr.placeholder:null}};var ro=n.memo((function(e){var r=t.useRef(null),i=_e((function(){r.current&&(clearTimeout(r.current),r.current=null)}),[]),o=e.animate,a=e.onTransitionEnd,l=e.onClose,u=e.contextId,c=t.useState("open"===e.animate),s=c[0],d=c[1];t.useEffect((function(){return s?"open"!==o?(i(),d(!1),Zi):r.current?Zi:(r.current=setTimeout((function(){r.current=null,d(!1)})),i):Zi}),[o,s,i]);var p=_e((function(e){"height"===e.propertyName&&(a(),"close"===o&&l())}),[o,l,a]),f=to({isAnimatingOpenOnMount:s,animate:e.animate,placeholder:e.placeholder});return n.createElement(e.placeholder.tagName,{style:f,"data-rbd-placeholder-context-id":u,onTransitionEnd:p,ref:e.innerRef})})),no=n.createContext(null),io=function(e){function t(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).state={isVisible:Boolean(t.props.on),data:t.props.on,animate:t.props.shouldAnimate&&t.props.on?"open":"none"},t.onClose=function(){"close"===t.state.animate&&t.setState({isVisible:!1})},t}return o(t,e),t.getDerivedStateFromProps=function(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}},t.prototype.render=function(){if(!this.state.isVisible)return null;var e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)},t}(n.PureComponent),oo=5e3,ao=4500,lo=function(e,t){return t?Hr.drop(t.duration):e?Hr.snap:Hr.fluid},uo=function(e,t){return e?t?kr.drop:kr.combining:null};function co(e){return"DRAGGING"===e.type?function(e){var t=e.dimension.client,r=e.offset,n=e.combineWith,i=e.dropping,o=Boolean(n),a=function(e){return null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode}(e),l=Boolean(i),u=l?zr(r,o):Vr(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:lo(a,i),transform:u,opacity:uo(o,l),zIndex:l?ao:oo,pointerEvents:"none"}}(e):{transform:Vr((t=e).offset),transition:t.shouldAnimateDisplacement?null:"none"};var t}function so(e){var r=ui("draggable"),n=e.descriptor,i=e.registry,o=e.getDraggableRef,a=e.canDragInteractiveElements,l=e.shouldRespectForcePress,u=e.isEnabled,c=Ge((function(){return{canDragInteractiveElements:a,shouldRespectForcePress:l,isEnabled:u}}),[a,u,l]),d=_e((function(e){var t=o();return t||s(!1),function(e,t,r){void 0===r&&(r=Fe);var n=window.getComputedStyle(t),i=t.getBoundingClientRect(),o=tt(i,n),a=et(o,r);return{descriptor:e,placeholder:{client:o,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:o.marginBox.width,y:o.marginBox.height},client:o,page:a}}(n,t,e)}),[n,o]),p=Ge((function(){return{uniqueId:r,descriptor:n,options:c,getDimension:d}}),[n,d,c,r]),f=t.useRef(p),g=t.useRef(!0);Jn((function(){return i.draggable.register(f.current),function(){return i.draggable.unregister(f.current)}}),[i.draggable]),Jn((function(){if(g.current)g.current=!1;else{var e=f.current;f.current=p,i.draggable.update(p,e)}}),[p,i.draggable])}function po(e){e.preventDefault()}var fo=function(e,t){return e===t},go=function(e){var t=e.combine,r=e.destination;return r?r.droppableId:t?t.droppableId:null};function vo(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var mo={mapped:{type:"SECONDARY",offset:Fe,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:vo(null)}};var bo=Me((function(){var e,t,r,n=(e=ct((function(e,t){return{x:e,y:t}})),t=ct((function(e,t,r,n,i){return{isDragging:!0,isClone:t,isDropAnimating:Boolean(i),dropAnimation:i,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null}})),r=ct((function(e,r,n,i,o,a,l){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:o,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:l,snapshot:t(r,i,o,a,null)}}})),function(n,i){if(n.isDragging){if(n.critical.draggable.id!==i.draggableId)return null;var o=n.current.client.offset,a=n.dimensions.draggables[i.draggableId],l=or(n.impact),u=(s=n.impact).at&&"COMBINE"===s.at.type?s.at.combine.draggableId:null,c=n.forceShouldAnimate;return r(e(o.x,o.y),n.movementMode,a,i.isClone,l,u,c)}var s;if("DROP_ANIMATING"===n.phase){var d=n.completed;if(d.result.draggableId!==i.draggableId)return null;var p=i.isClone,f=n.dimensions.draggables[i.draggableId],g=d.result,v=g.mode,m=go(g),b=function(e){return e.combine?e.combine.draggableId:null}(g),h={duration:n.dropDuration,curve:jr,moveTo:n.newHomeClientOffset,opacity:b?kr.drop:null,scale:b?Wr.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:f,dropping:h,draggingOver:m,combineWith:b,mode:v,forceShouldAnimate:null,snapshot:t(v,p,m,b,h)}}}return null}),i=function(){var e=ct((function(e,t){return{x:e,y:t}})),t=ct(vo),r=ct((function(e,r,n){return void 0===r&&(r=null),{mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}}})),n=function(e){return e?r(Fe,e,!0):null},i=function(t,i,o,a){var l=o.displaced.visible[t],u=Boolean(a.inVirtualList&&a.effected[t]),c=xt(o),s=c&&c.draggableId===t?i:null;if(!l){if(!u)return n(s);if(o.displaced.invisible[t])return null;var d=Ue(a.displacedBy.point),p=e(d.x,d.y);return r(p,s,!0)}if(u)return n(s);var f=o.displacedBy.point,g=e(f.x,f.y);return r(g,s,l.shouldAnimate)};return function(e,t){if(e.isDragging)return e.critical.draggable.id===t.draggableId?null:i(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){var r=e.completed;return r.result.draggableId===t.draggableId?null:i(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return function(e,t){return n(e,t)||i(e,t)||mo}}),{dropAnimationFinished:Fr},null,{context:ni,pure:!0,areStatePropsEqual:fo})((function(e){var r=t.useRef(null),n=_e((function(e){r.current=e}),[]),i=_e((function(){return r.current}),[]),o=Ki(ci),a=o.contextId,l=o.dragHandleUsageInstructionsId,u=o.registry,c=Ki(no),s=c.type,d=c.droppableId,p=Ge((function(){return{id:e.draggableId,index:e.index,type:s,droppableId:d}}),[e.draggableId,e.index,s,d]),f=e.children,g=e.draggableId,v=e.isEnabled,m=e.shouldRespectForcePress,b=e.canDragInteractiveElements,h=e.isClone,y=e.mapped,x=e.dropAnimationFinished;h||so(Ge((function(){return{descriptor:p,registry:u,getDraggableRef:i,canDragInteractiveElements:b,shouldRespectForcePress:m,isEnabled:v}}),[p,u,i,b,m,v]));var I=Ge((function(){return v?{tabIndex:0,role:"button","aria-describedby":l,"data-rbd-drag-handle-draggable-id":g,"data-rbd-drag-handle-context-id":a,draggable:!1,onDragStart:po}:null}),[a,l,g,v]),D=_e((function(e){"DRAGGING"===y.type&&y.dropping&&"transform"===e.propertyName&&x()}),[x,y]),w=Ge((function(){var e=co(y),t="DRAGGING"===y.type&&y.dropping?D:null;return{innerRef:n,draggableProps:{"data-rbd-draggable-context-id":a,"data-rbd-draggable-id":g,style:e,onTransitionEnd:t},dragHandleProps:I}}),[a,I,g,y,D,n]),E=Ge((function(){return{draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}}),[p.droppableId,p.id,p.index,p.type]);return f(w,y.snapshot,E)}));function ho(e){return Ki(no).isUsingCloneFor!==e.draggableId||e.isClone?n.createElement(bo,e):null}var yo=function(e,t){return e===t.droppable.type},xo=function(e,t){return t.draggables[e.draggable.id]};var Io={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||s(!1),document.body}},Do=Me((function(){var e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=l({},e,{shouldAnimatePlaceholder:!1}),r=ct((function(e){return{draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}})),n=ct((function(n,i,o,a,l,u){var c=l.descriptor.id;if(l.descriptor.droppableId===n){var s=u?{render:u,dragging:r(l.descriptor)}:null,d={isDraggingOver:o,draggingOverWith:o?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!1,snapshot:d,useClone:s}}if(!i)return t;if(!a)return e;var p={isDraggingOver:o,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!0,snapshot:p,useClone:null}}));return function(r,i){var o=i.droppableId,a=i.type,l=!i.isDropDisabled,u=i.renderClone;if(r.isDragging){var c=r.critical;if(!yo(a,c))return t;var s=xo(c,r.dimensions),d=or(r.impact)===o;return n(o,l,d,d,s,u)}if("DROP_ANIMATING"===r.phase){var p=r.completed;if(!yo(a,p.critical))return t;var f=xo(p.critical,r.dimensions);return n(o,l,go(p.result)===o,or(p.impact)===o,f,u)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){var g=r.completed;if(!yo(a,g.critical))return t;var v=or(g.impact)===o,m=Boolean(g.impact.at&&"COMBINE"===g.impact.at.type),b=g.critical.droppable.id===o;return v?m?e:t:b?e:t}return t}}),{updateViewportMaxScroll:function(e){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e}}},null,{context:ni,pure:!0,areStatePropsEqual:fo})((function(e){var r=t.useContext(ci);r||s(!1);var o=r.contextId,a=r.isMovementAllowed,l=t.useRef(null),u=t.useRef(null),c=e.children,d=e.droppableId,p=e.type,f=e.mode,g=e.direction,v=e.ignoreContainerClipping,m=e.isDropDisabled,b=e.isCombineEnabled,h=e.snapshot,y=e.useClone,x=e.updateViewportMaxScroll,I=e.getContainerForClone,D=_e((function(){return l.current}),[]),w=_e((function(e){l.current=e}),[]),E=(_e((function(){return u.current}),[]),_e((function(e){u.current=e}),[])),C=_e((function(){a()&&x({maxScroll:gn()})}),[a,x]);!function(e){var r=t.useRef(null),n=Ki(ci),i=ui("droppable"),o=n.registry,a=n.marshal,l=si(e),u=Ge((function(){return{id:e.droppableId,type:e.type,mode:e.mode}}),[e.droppableId,e.mode,e.type]),c=t.useRef(u),d=Ge((function(){return ct((function(e,t){r.current||s(!1);var n={x:e,y:t};a.updateDroppableScroll(u.id,n)}))}),[u.id,a]),p=_e((function(){var e=r.current;return e&&e.env.closestScrollable?Vi(e.env.closestScrollable):Fe}),[]),f=_e((function(){var e=p();d(e.x,e.y)}),[p,d]),g=Ge((function(){return Kr(f)}),[f]),v=_e((function(){var e=r.current,t=Qi(e);e&&t||s(!1),e.scrollOptions.shouldPublishImmediately?f():g()}),[g,f]),m=_e((function(e,t){r.current&&s(!1);var i=l.current,o=i.getDroppableRef();o||s(!1);var a=zi(o),c={ref:o,descriptor:u,env:a,scrollOptions:t};r.current=c;var d=$i({ref:o,descriptor:u,env:a,windowScroll:e,direction:i.direction,isDropDisabled:i.isDropDisabled,isCombineEnabled:i.isCombineEnabled,shouldClipSubject:!i.ignoreContainerClipping}),p=a.closestScrollable;return p&&(p.setAttribute($n.contextId,n.contextId),p.addEventListener("scroll",v,Xi(c.scrollOptions))),d}),[n.contextId,u,v,l]),b=_e((function(){var e=r.current,t=Qi(e);return e&&t||s(!1),Vi(t)}),[]),h=_e((function(){var e=r.current;e||s(!1);var t=Qi(e);r.current=null,t&&(g.cancel(),t.removeAttribute($n.contextId),t.removeEventListener("scroll",v,Xi(e.scrollOptions)))}),[v,g]),y=_e((function(e){var t=r.current;t||s(!1);var n=Qi(t);n||s(!1),n.scrollTop+=e.y,n.scrollLeft+=e.x}),[]),x=Ge((function(){return{getDimensionAndWatchScroll:m,getScrollWhileDragging:b,dragStopped:h,scroll:y}}),[h,m,b,y]),I=Ge((function(){return{uniqueId:i,descriptor:u,callbacks:x}}),[x,u,i]);Jn((function(){return c.current=I.descriptor,o.droppable.register(I),function(){r.current&&h(),o.droppable.unregister(I)}}),[x,u,h,I,a,o.droppable]),Jn((function(){r.current&&a.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)}),[e.isDropDisabled,a]),Jn((function(){r.current&&a.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,a])}({droppableId:d,type:p,mode:f,direction:g,isDropDisabled:m,isCombineEnabled:b,ignoreContainerClipping:v,getDroppableRef:D});var S=n.createElement(io,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},(function(e){var t=e.onClose,r=e.data,i=e.animate;return n.createElement(ro,{placeholder:r,onClose:t,innerRef:E,animate:i,contextId:o,onTransitionEnd:C})})),P=Ge((function(){return{innerRef:w,placeholder:S,droppableProps:{"data-rbd-droppable-id":d,"data-rbd-droppable-context-id":o}}}),[o,d,S,w]),O=y?y.dragging.draggableId:null,A=Ge((function(){return{droppableId:d,type:p,isUsingCloneFor:O}}),[d,O,p]);return n.createElement(no.Provider,{value:A},c(P,h),function(){if(!y)return null;var e=y.dragging,t=y.render,r=n.createElement(ho,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(function(r,n){return t(r,n,e)}));return i.createPortal(r,I())}())}));Do.defaultProps=Io,e.DragDropContext=function(e){var t=Ge((function(){return""+ji++}),[]),r=e.dragHandleUsageInstructions||m;return n.createElement(d,null,(function(i){return n.createElement(Fi,{nonce:e.nonce,contextId:t,setCallbacks:i,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)}))},e.Draggable=function(e){var t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=Boolean(e.disableInteractiveElementBlocking),i=Boolean(e.shouldRespectForcePress);return n.createElement(ho,l({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:i}))},e.Droppable=Do,e.resetServerContext=function(){ji=0,ai=0},e.useKeyboardSensor=Di,e.useMouseSensor=hi,e.useTouchSensor=Ei,Object.defineProperty(e,"__esModule",{value:!0})}));
