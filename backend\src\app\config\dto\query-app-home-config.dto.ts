import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, <PERSON>In, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class QueryAppHomeConfigDto {
  @ApiProperty({ 
    description: '页码', 
    example: 1, 
    required: false,
    minimum: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number = 1;

  @ApiProperty({ 
    description: '每页数量', 
    example: 10, 
    required: false,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  pageSize?: number = 10;

  @ApiProperty({
    description: '配置名称（模糊搜索）',
    example: '默认',
    required: false
  })
  @IsOptional()
  @Transform(({ value }) => value === null || value === undefined || value === '' || value === 'undefined' ? undefined : value)
  @IsString({ message: '配置名称必须是字符串' })
  configName?: string;

  @ApiProperty({
    description: '状态：1-启用，0-禁用',
    example: 1,
    required: false
  })
  @IsOptional()
  @Transform(({ value }) => value === null || value === undefined || value === '' || value === 'undefined' ? undefined : Number(value))
  @IsNumber({}, { message: '状态必须是数字' })
  @IsIn([0, 1], { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ 
    description: '排序字段', 
    example: 'createTime', 
    required: false,
    enum: ['createTime', 'updateTime', 'sortOrder', 'configName']
  })
  @IsOptional()
  @IsString({ message: '排序字段必须是字符串' })
  @IsIn(['createTime', 'updateTime', 'sortOrder', 'configName'], { 
    message: '排序字段必须是createTime、updateTime、sortOrder或configName之一' 
  })
  sortBy?: string = 'createTime';

  @ApiProperty({ 
    description: '排序方向', 
    example: 'DESC', 
    required: false,
    enum: ['ASC', 'DESC']
  })
  @IsOptional()
  @IsString({ message: '排序方向必须是字符串' })
  @IsIn(['ASC', 'DESC'], { message: '排序方向必须是ASC或DESC' })
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
