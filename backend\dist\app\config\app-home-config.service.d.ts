import { Repository, DataSource } from 'typeorm';
import { AppHomeConfig, AppHomeRecommendedGame, AppHomeGameCategory, AppHomeCategoryGame } from './entities';
import { AdConfig } from './entities/ad-config.entity';
import { Application } from '../entities/application.entity';
import { CreateAppHomeConfigDto, UpdateAppHomeConfigDto, QueryAppHomeConfigDto, AppHomeConfigDetailResponseDto, AppHomeConfigPageResponseDto } from './dto';
export declare class AppHomeConfigService {
    private appHomeConfigRepository;
    private recommendedGameRepository;
    private gameCategoryRepository;
    private categoryGameRepository;
    private adConfigRepository;
    private applicationRepository;
    private dataSource;
    constructor(appHomeConfigRepository: Repository<AppHomeConfig>, recommendedGameRepository: Repository<AppHomeRecommendedGame>, gameCategoryRepository: Repository<AppHomeGameCategory>, categoryGameRepository: Repository<AppHomeCategoryGame>, adConfigRepository: Repository<AdConfig>, applicationRepository: Repository<Application>, dataSource: DataSource);
    create(createDto: CreateAppHomeConfigDto, userId: number): Promise<AppHomeConfig>;
    findAll(query: QueryAppHomeConfigDto): Promise<AppHomeConfigPageResponseDto>;
    findOne(id: number): Promise<AppHomeConfigDetailResponseDto>;
    update(id: number, updateDto: UpdateAppHomeConfigDto, userId: number): Promise<AppHomeConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    toggleStatus(id: number, userId: number): Promise<{
        message: string;
        status: number;
    }>;
    getAppHomeConfig(id: number): Promise<{
        id: number;
        configName: string;
        templateType: string;
        ads: {
            topBanner: {
                id: number;
                adIdentifier: string;
                title: string;
                adType: import("./entities/ad-config.entity").AdType;
                imageItems: import("./entities/ad-config.entity").ImageItem[];
            } | null;
            carousel: {
                id: number;
                adIdentifier: string;
                title: string;
                adType: import("./entities/ad-config.entity").AdType;
                imageItems: import("./entities/ad-config.entity").ImageItem[];
            } | null;
            homeGrid: {
                id: number;
                adIdentifier: string;
                title: string;
                adType: import("./entities/ad-config.entity").AdType;
                imageItems: import("./entities/ad-config.entity").ImageItem[];
            } | null;
            splashPopup: {
                id: number;
                adIdentifier: string;
                title: string;
                adType: import("./entities/ad-config.entity").AdType;
                imageItems: import("./entities/ad-config.entity").ImageItem[];
            } | null;
        };
        recommendedGames: {
            id: number;
            name: string;
            iconUrl: string;
            posterUrl: string;
            categories: string[];
            rtp: number;
            volatility: string;
            status: string;
        }[];
        gameCategories: {
            id: number;
            categoryTitle: import("./entities").CategoryTitle;
            games: {
                id: number;
                name: string;
                iconUrl: string;
                posterUrl: string;
                categories: string[];
                rtp: number;
                volatility: string;
                status: string;
            }[];
        }[];
    }>;
    private validateAdConfigs;
    private validateApplications;
    private hasAdConfigUpdates;
    private transformToDetailResponse;
    private transformToAppResponse;
    private transformAdInfo;
    private transformGameInfo;
    private validateBoxModeConfig;
}
