import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON>ber, IsString, Min, Max, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';
import { AdType, JumpType } from '../entities/ad-config.entity';

export class AdConfigQueryDto {
  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number = 1;

  @ApiProperty({ description: '每页数量', example: 10, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(500, { message: '每页数量不能大于500' })
  pageSize?: number = 10;

  @ApiProperty({
    description: '广告类型筛选：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏，7-首页4宫格',
    example: 1,
    required: false,
    enum: AdType
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '广告类型必须是数字' })
  @IsIn([1, 2, 3, 4, 5, 6, 7], { message: '广告类型必须是1-7之间的数字' })
  adType?: AdType;

  @ApiProperty({ 
    description: '跳转类型筛选：1-内部路由，2-iframe页面', 
    example: 1, 
    required: false,
    enum: JumpType
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '跳转类型必须是数字' })
  @IsIn([1, 2], { message: '跳转类型必须是1或2' })
  jumpType?: JumpType;

  @ApiProperty({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => value === null || value === undefined || value === '' ? undefined : parseInt(value))
  @IsNumber({}, { message: '状态必须是数字' })
  @IsIn([0, 1], { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ description: '广告标题搜索', example: '轮播', required: false })
  @IsOptional()
  @IsString({ message: '广告标题必须是字符串' })
  title?: string;

  @ApiProperty({ description: '广告标识搜索', example: 'banner', required: false })
  @IsOptional()
  @IsString({ message: '广告标识必须是字符串' })
  adIdentifier?: string;
}

export class AdConfigListDto {
  @ApiProperty({ description: '广告配置ID' })
  id: number;

  @ApiProperty({ description: '广告标识' })
  adIdentifier: string;

  @ApiProperty({ description: '广告类型' })
  adType: AdType;

  @ApiProperty({ description: '广告类型名称' })
  adTypeName: string;

  @ApiProperty({ description: '广告标题' })
  title: string;

  @ApiProperty({ description: '图片URL数组' })
  images: string[];

  @ApiProperty({ description: '跳转类型' })
  jumpType: JumpType;

  @ApiProperty({ description: '跳转类型名称' })
  jumpTypeName: string;

  @ApiProperty({ description: '跳转目标' })
  jumpTarget: string;

  @ApiProperty({ description: '排序' })
  sortOrder: number;

  @ApiProperty({ description: '状态：1-启用，0-禁用' })
  status: number;

  @ApiProperty({ description: '备注说明' })
  remark: string;

  @ApiProperty({ description: '创建人ID' })
  createdBy: number;

  @ApiProperty({ description: '更新人ID' })
  updatedBy: number;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  updateTime: Date;

  @ApiProperty({ description: '创建人信息', required: false })
  creator?: {
    id: number;
    username: string;
  };

  @ApiProperty({ description: '更新人信息', required: false })
  updater?: {
    id: number;
    username: string;
  };
}
