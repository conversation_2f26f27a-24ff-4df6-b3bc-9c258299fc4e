import { ApiProperty } from '@nestjs/swagger';
import { 
  IsNotEmpty, 
  IsNumber, 
  IsString, 
  IsOptional, 
  Min, 
  Max, 
  Length, 
  IsIn, 
  IsArray, 
  ArrayMinSize, 
  ArrayMaxSize,
  ValidateNested,
  IsObject
} from 'class-validator';
import { Type } from 'class-transformer';
import { CategoryTitle } from '../entities/app-home-game-category.entity';
import { FloatContent } from '../entities/app-home-config.entity';

// 浮点内容DTO
export class FloatContentDto {
  @ApiProperty({ description: '唯一标识', example: 'float_1' })
  @IsNotEmpty({ message: '唯一标识不能为空' })
  @IsString({ message: '唯一标识必须是字符串' })
  id: string;

  @ApiProperty({
    description: '内容类型',
    example: 'announcement',
    enum: ['announcement', 'promotion', 'customer_service', 'checkin_reminder', 'recharge_bonus']
  })
  @IsNotEmpty({ message: '内容类型不能为空' })
  @IsIn(['announcement', 'promotion', 'customer_service', 'checkin_reminder', 'recharge_bonus'],
    { message: '内容类型必须是有效值' })
  type: string;

  @ApiProperty({ description: '标题', example: '系统公告' })
  @IsNotEmpty({ message: '标题不能为空' })
  @IsString({ message: '标题必须是字符串' })
  @Length(1, 100, { message: '标题长度必须在1-100字符之间' })
  title: string;

  @ApiProperty({ description: '内容', example: '欢迎使用我们的应用' })
  @IsNotEmpty({ message: '内容不能为空' })
  @IsString({ message: '内容必须是字符串' })
  @Length(1, 500, { message: '内容长度必须在1-500字符之间' })
  content: string;

  @ApiProperty({ description: '图片URL', example: 'https://example.com/image.jpg', required: false })
  @IsOptional()
  @IsString({ message: '图片URL必须是字符串' })
  imageUrl?: string;

  @ApiProperty({ description: '跳转类型', example: 'internal_route', required: false })
  @IsOptional()
  @IsIn(['internal_route', 'iframe_page'], { message: '跳转类型必须是有效值' })
  jumpType?: 'internal_route' | 'iframe_page';

  @ApiProperty({ description: '跳转目标', example: '/home', required: false })
  @IsOptional()
  @IsString({ message: '跳转目标必须是字符串' })
  jumpTarget?: string;

  @ApiProperty({
    description: '位置配置',
    example: { x: 50, y: 20 }
  })
  @IsNotEmpty({ message: '位置配置不能为空' })
  @IsObject({ message: '位置配置必须是对象' })
  position: {
    x: number;
    y: number;
  };

  @ApiProperty({ description: '样式配置', required: false })
  @IsOptional()
  @IsObject({ message: '样式配置必须是对象' })
  style?: {
    width?: number;
    height?: number;
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: number;
  };

  @ApiProperty({ description: '显示时间配置', required: false })
  @IsOptional()
  @IsObject({ message: '显示时间配置必须是对象' })
  displayTime?: {
    startTime?: string;
    endTime?: string;
    duration?: number;
  };

  @ApiProperty({ description: '排序', example: 1 })
  @IsNotEmpty({ message: '排序不能为空' })
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder: number;

  @ApiProperty({ description: '状态：1-启用，0-禁用', example: 1 })
  @IsNotEmpty({ message: '状态不能为空' })
  @IsIn([0, 1], { message: '状态必须是0或1' })
  status: number;
}

// 推荐游戏DTO
export class RecommendedGameDto {
  @ApiProperty({ description: '游戏应用ID', example: 1 })
  @IsNotEmpty({ message: '游戏应用ID不能为空' })
  @IsNumber({}, { message: '游戏应用ID必须是数字' })
  applicationId: number;

  @ApiProperty({ description: '排序，数字越小越靠前', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder?: number;
}

// 分类组游戏DTO
export class CategoryGameDto {
  @ApiProperty({ description: '游戏应用ID', example: 1 })
  @IsNotEmpty({ message: '游戏应用ID不能为空' })
  @IsNumber({}, { message: '游戏应用ID必须是数字' })
  applicationId: number;

  @ApiProperty({ description: '排序，数字越小越靠前', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder?: number;
}

// 游戏分类组DTO
export class GameCategoryDto {
  @ApiProperty({ 
    description: '分类标题（多语言支持）', 
    example: { "zh-CN": "热门游戏", "en-US": "Hot Games" } 
  })
  @IsNotEmpty({ message: '分类标题不能为空' })
  @IsObject({ message: '分类标题必须是对象格式' })
  categoryTitle: CategoryTitle;

  @ApiProperty({ description: '排序，数字越小越靠前', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder?: number;

  @ApiProperty({ 
    description: '分类下的游戏列表（至少4个）', 
    type: [CategoryGameDto],
    minItems: 4
  })
  @IsNotEmpty({ message: '分类游戏列表不能为空' })
  @IsArray({ message: '分类游戏必须是数组格式' })
  @ArrayMinSize(4, { message: '每个分类至少需要4个游戏' })
  @ValidateNested({ each: true })
  @Type(() => CategoryGameDto)
  games: CategoryGameDto[];
}

export class CreateAppHomeConfigDto {
  @ApiProperty({ 
    description: '配置名称', 
    example: '默认首页配置' 
  })
  @IsNotEmpty({ message: '配置名称不能为空' })
  @IsString({ message: '配置名称必须是字符串' })
  @Length(1, 100, { message: '配置名称长度必须在1-100个字符之间' })
  configName: string;

  @ApiProperty({ 
    description: '配置描述', 
    example: '系统默认的APP首页配置', 
    required: false 
  })
  @IsOptional()
  @IsString({ message: '配置描述必须是字符串' })
  @Length(0, 500, { message: '配置描述长度不能超过500个字符' })
  description?: string;

  @ApiProperty({
    description: '顶部Banner广告ID（必填）',
    example: 1
  })
  @IsNotEmpty({ message: '顶部Banner广告ID不能为空' })
  @IsNumber({}, { message: '顶部Banner广告ID必须是数字' })
  topBannerAdId: number;

  @ApiProperty({ 
    description: '轮播广告ID（必填）', 
    example: 2 
  })
  @IsNotEmpty({ message: '轮播广告ID不能为空' })
  @IsNumber({}, { message: '轮播广告ID必须是数字' })
  carouselAdId: number;

  @ApiProperty({
    description: '首页6宫格广告ID（BOX模式专用）',
    example: 3,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: '首页6宫格广告ID必须是数字' })
  homeGridAdId?: number;

  @ApiProperty({ 
    description: '开屏弹窗广告ID（可选）', 
    example: 4, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '开屏弹窗广告ID必须是数字' })
  splashPopupAdId?: number;

  @ApiProperty({
    description: '浮点广告ID（可选）',
    example: 5,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: '浮点广告ID必须是数字' })
  floatAdId?: number;

  @ApiProperty({
    description: '首页模板类型',
    example: 'box',
    enum: ['box', 'classic', 'card', 'waterfall'],
    required: false
  })
  @IsOptional()
  @IsIn(['box', 'classic', 'card', 'waterfall'], { message: '模板类型必须是有效值' })
  templateType?: string;

  @ApiProperty({
    description: '浮点内容配置列表（BOX模式最多3个）',
    type: [FloatContentDto],
    required: false,
    maxItems: 3
  })
  @IsOptional()
  @IsArray({ message: '浮点内容必须是数组格式' })
  @ArrayMaxSize(3, { message: '浮点内容最多3个' })
  @ValidateNested({ each: true })
  @Type(() => FloatContentDto)
  floatContents?: FloatContentDto[];

  @ApiProperty({
    description: '推荐游戏列表（必须6个）',
    type: [RecommendedGameDto],
    minItems: 6,
    maxItems: 6
  })
  @IsNotEmpty({ message: '推荐游戏列表不能为空' })
  @IsArray({ message: '推荐游戏必须是数组格式' })
  @ArrayMinSize(6, { message: '必须配置6个推荐游戏' })
  @ArrayMaxSize(6, { message: '推荐游戏不能超过6个' })
  @ValidateNested({ each: true })
  @Type(() => RecommendedGameDto)
  recommendedGames: RecommendedGameDto[];

  @ApiProperty({ 
    description: '游戏分类组列表（至少4个分类）', 
    type: [GameCategoryDto],
    minItems: 4
  })
  @IsNotEmpty({ message: '游戏分类组列表不能为空' })
  @IsArray({ message: '游戏分类组必须是数组格式' })
  @ArrayMinSize(4, { message: '至少需要4个游戏分类组' })
  @ValidateNested({ each: true })
  @Type(() => GameCategoryDto)
  gameCategories: GameCategoryDto[];

  @ApiProperty({ 
    description: '排序，数字越小越靠前', 
    example: 1, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder?: number;

  @ApiProperty({ 
    description: '状态：1-启用，0-禁用', 
    example: 1, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '状态必须是数字' })
  @IsIn([0, 1], { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ 
    description: '备注说明', 
    example: '默认配置，用于APP首页展示', 
    required: false 
  })
  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  @Length(0, 500, { message: '备注长度不能超过500个字符' })
  remark?: string;
}
