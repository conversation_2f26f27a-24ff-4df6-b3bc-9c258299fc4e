"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryAppHomeConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class QueryAppHomeConfigDto {
    page = 1;
    pageSize = 10;
    configName;
    status;
    sortBy = 'createTime';
    sortOrder = 'DESC';
}
exports.QueryAppHomeConfigDto = QueryAppHomeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        example: 1,
        required: false,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    __metadata("design:type", Number)
], QueryAppHomeConfigDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        example: 10,
        required: false,
        minimum: 1,
        maximum: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_validator_1.Max)(100, { message: '每页数量不能大于100' }),
    __metadata("design:type", Number)
], QueryAppHomeConfigDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置名称（模糊搜索）',
        example: '默认',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === null || value === undefined || value === '' || value === 'undefined' ? undefined : value),
    (0, class_validator_1.IsString)({ message: '配置名称必须是字符串' }),
    __metadata("design:type", String)
], QueryAppHomeConfigDto.prototype, "configName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态：1-启用，0-禁用',
        example: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === null || value === undefined || value === '' || value === 'undefined' ? undefined : Number(value)),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], QueryAppHomeConfigDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序字段',
        example: 'createTime',
        required: false,
        enum: ['createTime', 'updateTime', 'sortOrder', 'configName']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '排序字段必须是字符串' }),
    (0, class_validator_1.IsIn)(['createTime', 'updateTime', 'sortOrder', 'configName'], {
        message: '排序字段必须是createTime、updateTime、sortOrder或configName之一'
    }),
    __metadata("design:type", String)
], QueryAppHomeConfigDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序方向',
        example: 'DESC',
        required: false,
        enum: ['ASC', 'DESC']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '排序方向必须是字符串' }),
    (0, class_validator_1.IsIn)(['ASC', 'DESC'], { message: '排序方向必须是ASC或DESC' }),
    __metadata("design:type", String)
], QueryAppHomeConfigDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=query-app-home-config.dto.js.map