"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryApplicationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class QueryApplicationDto {
    page = 1;
    pageSize = 10;
    search;
    providerId;
    categories;
    status;
    tags;
    platforms;
    sortBy = 'createdAt';
    sortOrder = 'DESC';
    features;
    hasDemo;
    supplierIdentifier;
}
exports.QueryApplicationDto = QueryApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], QueryApplicationDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_validator_1.Max)(500, { message: '每页数量不能大于500' }),
    __metadata("design:type", Number)
], QueryApplicationDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用名称或简称代码搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryApplicationDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '供应商ID筛选', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], QueryApplicationDto.prototype, "providerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类筛选', example: ['slot_games'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], QueryApplicationDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态筛选', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === null || value === undefined || value === '' || value === 'undefined' ? undefined : value),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryApplicationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签筛选', example: ['hot', 'featured'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], QueryApplicationDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '平台筛选', example: ['ios', 'android'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], QueryApplicationDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序字段', example: 'createdAt', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryApplicationDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序方向', example: 'DESC', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryApplicationDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏特性筛选', example: ['free_spins'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], QueryApplicationDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持演示模式', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryApplicationDto.prototype, "hasDemo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '供应商标识符筛选', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryApplicationDto.prototype, "supplierIdentifier", void 0);
//# sourceMappingURL=query-application.dto.js.map