"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("./entities");
const ad_config_entity_1 = require("./entities/ad-config.entity");
const application_entity_1 = require("../entities/application.entity");
let AppHomeConfigService = class AppHomeConfigService {
    appHomeConfigRepository;
    recommendedGameRepository;
    gameCategoryRepository;
    categoryGameRepository;
    adConfigRepository;
    applicationRepository;
    dataSource;
    constructor(appHomeConfigRepository, recommendedGameRepository, gameCategoryRepository, categoryGameRepository, adConfigRepository, applicationRepository, dataSource) {
        this.appHomeConfigRepository = appHomeConfigRepository;
        this.recommendedGameRepository = recommendedGameRepository;
        this.gameCategoryRepository = gameCategoryRepository;
        this.categoryGameRepository = categoryGameRepository;
        this.adConfigRepository = adConfigRepository;
        this.applicationRepository = applicationRepository;
        this.dataSource = dataSource;
    }
    async create(createDto, userId) {
        const existingConfig = await this.appHomeConfigRepository.findOne({
            where: { configName: createDto.configName },
        });
        if (existingConfig) {
            throw new common_1.ConflictException(`配置名称 ${createDto.configName} 已存在`);
        }
        await this.validateAdConfigs(createDto);
        await this.validateBoxModeConfig(createDto);
        await this.validateApplications(createDto);
        return await this.dataSource.transaction(async (manager) => {
            const homeConfig = manager.create(entities_1.AppHomeConfig, {
                configName: createDto.configName,
                description: createDto.description,
                topBannerAdId: createDto.topBannerAdId,
                carouselAdId: createDto.carouselAdId,
                homeGridAdId: createDto.homeGridAdId,
                splashPopupAdId: createDto.splashPopupAdId,
                templateType: createDto.templateType ?? 'box',
                status: createDto.status ?? 1,
                sortOrder: createDto.sortOrder ?? 0,
                remark: createDto.remark,
                createdBy: userId,
                updatedBy: userId,
            });
            const savedConfig = await manager.save(entities_1.AppHomeConfig, homeConfig);
            if (createDto.recommendedGames?.length) {
                const recommendedGames = createDto.recommendedGames.map((game, index) => manager.create(entities_1.AppHomeRecommendedGame, {
                    homeConfigId: savedConfig.id,
                    applicationId: game.applicationId,
                    sortOrder: game.sortOrder ?? index + 1,
                    createdBy: userId,
                    updatedBy: userId,
                }));
                await manager.save(entities_1.AppHomeRecommendedGame, recommendedGames);
            }
            if (createDto.gameCategories?.length) {
                for (const [categoryIndex, category] of createDto.gameCategories.entries()) {
                    const gameCategory = manager.create(entities_1.AppHomeGameCategory, {
                        homeConfigId: savedConfig.id,
                        categoryTitle: category.categoryTitle,
                        sortOrder: category.sortOrder ?? categoryIndex + 1,
                        createdBy: userId,
                        updatedBy: userId,
                    });
                    const savedCategory = await manager.save(entities_1.AppHomeGameCategory, gameCategory);
                    if (category.games?.length) {
                        const categoryGames = category.games.map((game, gameIndex) => manager.create(entities_1.AppHomeCategoryGame, {
                            homeCategoryId: savedCategory.id,
                            applicationId: game.applicationId,
                            sortOrder: game.sortOrder ?? gameIndex + 1,
                            createdBy: userId,
                            updatedBy: userId,
                        }));
                        await manager.save(entities_1.AppHomeCategoryGame, categoryGames);
                    }
                }
            }
            return savedConfig;
        });
    }
    async findAll(query) {
        const { page = 1, pageSize = 10, configName, status, sortBy = 'createTime', sortOrder = 'DESC' } = query;
        const queryBuilder = this.appHomeConfigRepository
            .createQueryBuilder('config')
            .leftJoinAndSelect('config.creator', 'creator')
            .leftJoinAndSelect('config.updater', 'updater')
            .leftJoin('config.recommendedGames', 'recommendedGames')
            .leftJoin('config.gameCategories', 'gameCategories')
            .addSelect('COUNT(DISTINCT recommendedGames.id)', 'recommendedGameCount')
            .addSelect('COUNT(DISTINCT gameCategories.id)', 'categoryCount')
            .groupBy('config.id')
            .addGroupBy('creator.id')
            .addGroupBy('updater.id');
        if (configName) {
            queryBuilder.andWhere('config.configName ILIKE :configName', {
                configName: `%${configName}%`
            });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('config.status = :status', { status });
        }
        const orderDirection = sortOrder.toUpperCase();
        switch (sortBy) {
            case 'createTime':
                queryBuilder.orderBy('config.createTime', orderDirection);
                break;
            case 'updateTime':
                queryBuilder.orderBy('config.updateTime', orderDirection);
                break;
            case 'sortOrder':
                queryBuilder.orderBy('config.sortOrder', orderDirection);
                break;
            case 'configName':
                queryBuilder.orderBy('config.configName', orderDirection);
                break;
            default:
                queryBuilder.orderBy('config.createTime', 'DESC');
        }
        const offset = (page - 1) * pageSize;
        queryBuilder.skip(offset).take(pageSize);
        const [configs, total] = await queryBuilder.getManyAndCount();
        const list = configs.map(config => ({
            id: config.id,
            configName: config.configName,
            description: config.description,
            status: config.status,
            sortOrder: config.sortOrder,
            templateType: config.templateType,
            recommendedGameCount: parseInt(config.recommendedGameCount) || 0,
            categoryCount: parseInt(config.categoryCount) || 0,
            createTime: config.createTime,
            updateTime: config.updateTime,
        }));
        const totalPages = Math.ceil(total / pageSize);
        return {
            list,
            total,
            page,
            pageSize,
            totalPages,
        };
    }
    async findOne(id) {
        const config = await this.appHomeConfigRepository.findOne({
            where: { id },
            relations: [
                'creator',
                'updater',
                'topBannerAd',
                'carouselAd',
                'homeGridAd',
                'splashPopupAd',
                'floatAd',
                'recommendedGames',
                'recommendedGames.application',
                'gameCategories',
                'gameCategories.categoryGames',
                'gameCategories.categoryGames.application',
            ],
            order: {
                recommendedGames: { sortOrder: 'ASC' },
                gameCategories: {
                    sortOrder: 'ASC',
                    categoryGames: { sortOrder: 'ASC' }
                },
            },
        });
        if (!config) {
            throw new common_1.NotFoundException(`ID为 ${id} 的首页配置不存在`);
        }
        return this.transformToDetailResponse(config);
    }
    async update(id, updateDto, userId) {
        const config = await this.appHomeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException(`ID为 ${id} 的首页配置不存在`);
        }
        if (updateDto.configName && updateDto.configName !== config.configName) {
            const existingConfig = await this.appHomeConfigRepository.findOne({
                where: { configName: updateDto.configName },
            });
            if (existingConfig) {
                throw new common_1.ConflictException(`配置名称 ${updateDto.configName} 已存在`);
            }
        }
        if (this.hasAdConfigUpdates(updateDto)) {
            await this.validateAdConfigs(updateDto);
        }
        if (updateDto.templateType || updateDto.gameCategories || updateDto.floatContents !== undefined) {
            const validationDto = {
                templateType: updateDto.templateType || config.templateType,
                homeGridAdId: updateDto.homeGridAdId !== undefined ? updateDto.homeGridAdId : config.homeGridAdId,
                gameCategories: updateDto.gameCategories || [],
                floatContents: updateDto.floatContents !== undefined ? updateDto.floatContents : config.floatContents,
            };
            await this.validateBoxModeConfig(validationDto);
        }
        if (updateDto.recommendedGames || updateDto.gameCategories) {
            await this.validateApplications(updateDto);
        }
        return await this.dataSource.transaction(async (manager) => {
            Object.assign(config, {
                ...updateDto,
                updatedBy: userId,
            });
            const savedConfig = await manager.save(entities_1.AppHomeConfig, config);
            if (updateDto.recommendedGames) {
                await manager.delete(entities_1.AppHomeRecommendedGame, { homeConfigId: id });
                if (updateDto.recommendedGames.length > 0) {
                    const recommendedGames = updateDto.recommendedGames.map((game, index) => manager.create(entities_1.AppHomeRecommendedGame, {
                        homeConfigId: id,
                        applicationId: game.applicationId,
                        sortOrder: game.sortOrder ?? index + 1,
                        createdBy: userId,
                        updatedBy: userId,
                    }));
                    await manager.save(entities_1.AppHomeRecommendedGame, recommendedGames);
                }
            }
            if (updateDto.gameCategories) {
                const existingCategories = await manager.find(entities_1.AppHomeGameCategory, {
                    where: { homeConfigId: id },
                });
                for (const category of existingCategories) {
                    await manager.delete(entities_1.AppHomeCategoryGame, { homeCategoryId: category.id });
                }
                await manager.delete(entities_1.AppHomeGameCategory, { homeConfigId: id });
                if (updateDto.gameCategories.length > 0) {
                    for (const [categoryIndex, category] of updateDto.gameCategories.entries()) {
                        const gameCategory = manager.create(entities_1.AppHomeGameCategory, {
                            homeConfigId: id,
                            categoryTitle: category.categoryTitle,
                            sortOrder: category.sortOrder ?? categoryIndex + 1,
                            createdBy: userId,
                            updatedBy: userId,
                        });
                        const savedCategory = await manager.save(entities_1.AppHomeGameCategory, gameCategory);
                        if (category.games?.length) {
                            const categoryGames = category.games.map((game, gameIndex) => manager.create(entities_1.AppHomeCategoryGame, {
                                homeCategoryId: savedCategory.id,
                                applicationId: game.applicationId,
                                sortOrder: game.sortOrder ?? gameIndex + 1,
                                createdBy: userId,
                                updatedBy: userId,
                            }));
                            await manager.save(entities_1.AppHomeCategoryGame, categoryGames);
                        }
                    }
                }
            }
            return savedConfig;
        });
    }
    async remove(id) {
        const config = await this.appHomeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException(`ID为 ${id} 的首页配置不存在`);
        }
        await this.dataSource.transaction(async (manager) => {
            const categories = await manager.find(entities_1.AppHomeGameCategory, {
                where: { homeConfigId: id },
            });
            for (const category of categories) {
                await manager.delete(entities_1.AppHomeCategoryGame, { homeCategoryId: category.id });
            }
            await manager.delete(entities_1.AppHomeGameCategory, { homeConfigId: id });
            await manager.delete(entities_1.AppHomeRecommendedGame, { homeConfigId: id });
            await manager.remove(entities_1.AppHomeConfig, config);
        });
        return { message: '删除成功' };
    }
    async toggleStatus(id, userId) {
        const config = await this.appHomeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException(`ID为 ${id} 的首页配置不存在`);
        }
        config.status = config.status === 1 ? 0 : 1;
        config.updatedBy = userId;
        await this.appHomeConfigRepository.save(config);
        return {
            message: `${config.status === 1 ? '启用' : '禁用'}成功`,
            status: config.status
        };
    }
    async getAppHomeConfig(id) {
        const config = await this.appHomeConfigRepository.findOne({
            where: { id, status: 1 },
            relations: [
                'topBannerAd',
                'carouselAd',
                'homeGridAd',
                'splashPopupAd',
                'floatAd',
                'recommendedGames',
                'recommendedGames.application',
                'gameCategories',
                'gameCategories.categoryGames',
                'gameCategories.categoryGames.application',
            ],
            order: {
                recommendedGames: { sortOrder: 'ASC' },
                gameCategories: {
                    sortOrder: 'ASC',
                    categoryGames: { sortOrder: 'ASC' }
                },
            },
        });
        if (!config) {
            throw new common_1.NotFoundException(`ID为 ${id} 的首页配置不存在或已禁用`);
        }
        return this.transformToAppResponse(config);
    }
    async validateAdConfigs(dto) {
        const adIds = [
            dto.topBannerAdId,
            dto.carouselAdId,
            dto.homeGridAdId,
            dto.splashPopupAdId,
            dto.floatAdId,
        ].filter(id => id !== undefined && id !== null);
        if (adIds.length > 0) {
            const existingAds = await this.adConfigRepository.findByIds(adIds);
            const existingAdIds = existingAds.map(ad => ad.id);
            for (const adId of adIds) {
                if (!existingAdIds.includes(adId)) {
                    throw new common_1.BadRequestException(`广告配置ID ${adId} 不存在`);
                }
            }
            const disabledAds = existingAds.filter(ad => ad.status === 0);
            if (disabledAds.length > 0) {
                throw new common_1.BadRequestException(`以下广告配置已禁用：${disabledAds.map(ad => ad.title).join(', ')}`);
            }
        }
    }
    async validateApplications(dto) {
        const applicationIds = [];
        if (dto.recommendedGames) {
            applicationIds.push(...dto.recommendedGames.map(game => game.applicationId));
        }
        if (dto.gameCategories) {
            for (const category of dto.gameCategories) {
                if (category.games) {
                    applicationIds.push(...category.games.map(game => game.applicationId));
                }
            }
        }
        if (applicationIds.length > 0) {
            const uniqueIds = [...new Set(applicationIds)];
            const existingApps = await this.applicationRepository.findByIds(uniqueIds);
            const existingAppIds = existingApps.map(app => app.id);
            for (const appId of uniqueIds) {
                if (!existingAppIds.includes(appId)) {
                    throw new common_1.BadRequestException(`游戏应用ID ${appId} 不存在`);
                }
            }
            const inactiveApps = existingApps.filter(app => app.status !== 'active');
            if (inactiveApps.length > 0) {
                throw new common_1.BadRequestException(`以下游戏应用未激活：${inactiveApps.map(app => app.name).join(', ')}`);
            }
        }
    }
    hasAdConfigUpdates(dto) {
        return !!(dto.topBannerAdId !== undefined ||
            dto.carouselAdId !== undefined ||
            dto.homeGridAdId !== undefined ||
            dto.splashPopupAdId !== undefined ||
            dto.floatAdId !== undefined);
    }
    transformToDetailResponse(config) {
        return {
            id: config.id,
            configName: config.configName,
            description: config.description,
            status: config.status,
            sortOrder: config.sortOrder,
            remark: config.remark,
            templateType: config.templateType,
            floatContents: config.floatContents || [],
            createTime: config.createTime,
            updateTime: config.updateTime,
            topBannerAd: config.topBannerAd ? this.transformAdInfo(config.topBannerAd) : undefined,
            carouselAd: config.carouselAd ? this.transformAdInfo(config.carouselAd) : undefined,
            homeGridAd: config.homeGridAd ? this.transformAdInfo(config.homeGridAd) : undefined,
            splashPopupAd: config.splashPopupAd ? this.transformAdInfo(config.splashPopupAd) : undefined,
            floatAd: config.floatAd ? this.transformAdInfo(config.floatAd) : undefined,
            recommendedGames: config.recommendedGames?.map(rg => ({
                id: rg.id,
                sortOrder: rg.sortOrder,
                game: this.transformGameInfo(rg.application),
            })) || [],
            gameCategories: config.gameCategories?.map(gc => ({
                id: gc.id,
                categoryTitle: gc.categoryTitle,
                sortOrder: gc.sortOrder,
                status: gc.status,
                games: gc.categoryGames?.map(cg => ({
                    id: cg.id,
                    sortOrder: cg.sortOrder,
                    game: this.transformGameInfo(cg.application),
                })) || [],
            })) || [],
        };
    }
    transformToAppResponse(config) {
        return {
            id: config.id,
            configName: config.configName,
            templateType: config.templateType,
            floatContents: config.floatContents?.filter(content => content.status === 1) || [],
            ads: {
                topBanner: config.topBannerAd?.status === 1 ? this.transformAdInfo(config.topBannerAd) : null,
                carousel: config.carouselAd?.status === 1 ? this.transformAdInfo(config.carouselAd) : null,
                homeGrid: config.homeGridAd?.status === 1 ? this.transformAdInfo(config.homeGridAd) : null,
                splashPopup: config.splashPopupAd?.status === 1 ? this.transformAdInfo(config.splashPopupAd) : null,
                float: config.floatAd?.status === 1 ? this.transformAdInfo(config.floatAd) : null,
            },
            recommendedGames: config.recommendedGames
                ?.filter(rg => rg.status === 1 && rg.application?.status === 'active')
                .map(rg => this.transformGameInfo(rg.application)) || [],
            gameCategories: config.gameCategories
                ?.filter(gc => gc.status === 1)
                .map(gc => ({
                id: gc.id,
                categoryTitle: gc.categoryTitle,
                games: gc.categoryGames
                    ?.filter(cg => cg.status === 1 && cg.application?.status === 'active')
                    .map(cg => this.transformGameInfo(cg.application)) || [],
            })) || [],
        };
    }
    transformAdInfo(ad) {
        return {
            id: ad.id,
            adIdentifier: ad.adIdentifier,
            title: ad.title,
            adType: ad.adType,
            imageItems: ad.imageItems,
        };
    }
    transformGameInfo(app) {
        return {
            id: app.id,
            name: app.name,
            iconUrl: app.iconUrl,
            posterUrl: app.posterUrl,
            categories: app.categories || [],
            rtp: app.rtp,
            volatility: app.volatility,
            status: app.status,
        };
    }
    async validateBoxModeConfig(dto) {
        const templateType = dto.templateType || 'box';
        if (templateType === 'box') {
            if (!dto.homeGridAdId) {
                throw new common_1.BadRequestException('BOX模式需要配置6宫格广告');
            }
            if (!dto.gameCategories || dto.gameCategories.length < 4) {
                throw new common_1.BadRequestException('BOX模式至少需要4个游戏分类组');
            }
            dto.gameCategories.forEach((category, index) => {
                if (!category.games || category.games.length < 4) {
                    throw new common_1.BadRequestException(`第${index + 1}个游戏分类至少需要4个游戏`);
                }
            });
            if (dto.floatContents && dto.floatContents.length > 3) {
                throw new common_1.BadRequestException('BOX模式浮点内容最多3个');
            }
            if (dto.floatContents && dto.floatContents.length > 0) {
                const ids = dto.floatContents.map(content => content.id);
                const uniqueIds = new Set(ids);
                if (ids.length !== uniqueIds.size) {
                    throw new common_1.BadRequestException('浮点内容ID必须唯一');
                }
            }
        }
    }
};
exports.AppHomeConfigService = AppHomeConfigService;
exports.AppHomeConfigService = AppHomeConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.AppHomeConfig)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.AppHomeRecommendedGame)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.AppHomeGameCategory)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.AppHomeCategoryGame)),
    __param(4, (0, typeorm_1.InjectRepository)(ad_config_entity_1.AdConfig)),
    __param(5, (0, typeorm_1.InjectRepository)(application_entity_1.Application)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], AppHomeConfigService);
//# sourceMappingURL=app-home-config.service.js.map