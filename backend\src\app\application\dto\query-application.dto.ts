import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsArray, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class QueryApplicationDto {
  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', example: 10, required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1, { message: '每页数量不能小于1' })
  @Max(500, { message: '每页数量不能大于500' })
  pageSize?: number = 10;

  @ApiProperty({ description: '应用名称或简称代码搜索', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: '供应商ID筛选', required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  providerId?: number;

  @ApiProperty({ description: '分类筛选', example: ['slot_games'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  @ApiProperty({ description: '状态筛选', required: false })
  @IsOptional()
  @Transform(({ value }) => value === null || value === undefined || value === '' || value === 'undefined' ? undefined : value)
  @IsString()
  status?: string;

  @ApiProperty({ description: '标签筛选', example: ['hot', 'featured'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: '平台筛选', example: ['ios', 'android'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  platforms?: string[];

  @ApiProperty({ description: '排序字段', example: 'createdAt', required: false })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({ description: '排序方向', example: 'DESC', required: false })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({ description: '游戏特性筛选', example: ['free_spins'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @ApiProperty({ description: '是否支持演示模式', required: false })
  @IsOptional()
  hasDemo?: boolean;

  @ApiProperty({ description: '供应商标识符筛选', required: false })
  @IsOptional()
  @IsString()
  supplierIdentifier?: string;
}

export interface ApplicationListResponse {
  list: any[];
  total: number;
  page: number;
  pageSize: number;
}
