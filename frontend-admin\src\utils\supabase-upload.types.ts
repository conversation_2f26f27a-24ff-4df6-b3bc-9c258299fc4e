/**
 * Supabase上传工具类型定义
 */

// 上传结果类型
export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
  fileName: string;
}

// 压缩选项类型
export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
}

// 上传进度回调类型
export type ProgressCallback = (progress: number, current: number, total: number) => void;

// 上传配置类型
export interface UploadConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  s3: {
    accessKeyId: string;
    secretAccessKey: string;
    endpoint: string;
    region: string;
    bucket: string;
    folder: string;
  };
  upload: {
    maxFileSize: number;
    allowedTypes: string[];
    compressionOptions: {
      maxWidth: number;
      maxHeight: number;
      quality: number;
    };
  };
}

// 配置验证结果类型
export interface ConfigValidation {
  valid: boolean;
  errors: string[];
}
