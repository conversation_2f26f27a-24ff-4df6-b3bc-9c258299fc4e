import { ApiProperty } from '@nestjs/swagger';
import { CategoryTitle } from '../entities/app-home-game-category.entity';
import { ImageItem } from '../entities/ad-config.entity';

// 图片跳转项响应DTO
export class ImageItemResponseDto implements ImageItem {
  @ApiProperty({ description: '图片URL', example: '/uploads/ads/banner1.jpg' })
  imageUrl: string;

  @ApiProperty({ description: '跳转类型：1-内部路由，2-iframe页面', example: 1 })
  jumpType: number;

  @ApiProperty({ description: '跳转目标', example: '/games/hot' })
  jumpTarget: string;

  @ApiProperty({ description: '图片标题', example: '热门游戏', required: false })
  title?: string;

  @ApiProperty({ description: '图片描述', example: '查看最受欢迎的游戏', required: false })
  description?: string;
}



// 广告信息响应DTO
export class AdInfoResponseDto {
  @ApiProperty({ description: '广告ID', example: 1 })
  id: number;

  @ApiProperty({ description: '广告标识', example: 'banner_home_1' })
  adIdentifier: string;

  @ApiProperty({ description: '广告标题', example: '首页轮播广告' })
  title: string;

  @ApiProperty({ description: '广告类型', example: 1 })
  adType: number;

  @ApiProperty({
    description: '图片跳转项数组',
    type: [ImageItemResponseDto],
    example: [
      {
        imageUrl: '/uploads/ads/banner1.jpg',
        jumpType: 1,
        jumpTarget: '/games/popular',
        title: '热门游戏',
        description: '查看最受欢迎的游戏'
      }
    ]
  })
  imageItems: ImageItemResponseDto[];
}

// 游戏信息响应DTO
export class GameInfoResponseDto {
  @ApiProperty({ description: '游戏ID', example: 1 })
  id: number;

  @ApiProperty({ description: '游戏名称', example: '热门游戏1' })
  name: string;

  @ApiProperty({ description: '游戏图标URL', example: '/uploads/games/icon1.jpg' })
  iconUrl: string;

  @ApiProperty({ description: '游戏海报URL', example: '/uploads/games/poster1.jpg' })
  posterUrl: string;

  @ApiProperty({ description: '游戏分类', example: ['slot', 'popular'] })
  categories: string[];

  @ApiProperty({ description: '返还率', example: 96.5 })
  rtp: number;

  @ApiProperty({ description: '波动性', example: 'medium' })
  volatility: string;

  @ApiProperty({ description: '游戏状态', example: 'active' })
  status: string;
}

// 推荐游戏响应DTO
export class RecommendedGameResponseDto {
  @ApiProperty({ description: '关联ID', example: 1 })
  id: number;

  @ApiProperty({ description: '排序', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '游戏信息', type: GameInfoResponseDto })
  game: GameInfoResponseDto;
}

// 分类组游戏响应DTO
export class CategoryGameResponseDto {
  @ApiProperty({ description: '关联ID', example: 1 })
  id: number;

  @ApiProperty({ description: '排序', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '游戏信息', type: GameInfoResponseDto })
  game: GameInfoResponseDto;
}

// 游戏分类组响应DTO
export class GameCategoryResponseDto {
  @ApiProperty({ description: '分类组ID', example: 1 })
  id: number;

  @ApiProperty({ 
    description: '分类标题（多语言）', 
    example: { "zh-CN": "热门游戏", "en-US": "Hot Games" } 
  })
  categoryTitle: CategoryTitle;

  @ApiProperty({ description: '排序', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '状态', example: 1 })
  status: number;

  @ApiProperty({ description: '分类下的游戏列表', type: [CategoryGameResponseDto] })
  games: CategoryGameResponseDto[];
}

// APP首页配置详情响应DTO
export class AppHomeConfigDetailResponseDto {
  @ApiProperty({ description: '配置ID', example: 1 })
  id: number;

  @ApiProperty({ description: '配置名称', example: '默认首页配置' })
  configName: string;

  @ApiProperty({ description: '配置描述', example: '系统默认的APP首页配置' })
  description: string;

  @ApiProperty({ description: '状态', example: 1 })
  status: number;

  @ApiProperty({ description: '排序', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '备注', example: '默认配置' })
  remark: string;

  @ApiProperty({ description: '模板类型', example: 'box' })
  templateType: string;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createTime: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updateTime: Date;

  // 广告配置
  @ApiProperty({ description: '顶部Banner广告', type: AdInfoResponseDto, required: false })
  topBannerAd?: AdInfoResponseDto;

  @ApiProperty({ description: '轮播广告', type: AdInfoResponseDto, required: false })
  carouselAd?: AdInfoResponseDto;

  @ApiProperty({ description: '首页6宫格广告（BOX模式专用）', type: AdInfoResponseDto, required: false })
  homeGridAd?: AdInfoResponseDto;

  @ApiProperty({ description: '开屏弹窗广告', type: AdInfoResponseDto, required: false })
  splashPopupAd?: AdInfoResponseDto;

  // 推荐游戏和分类组
  @ApiProperty({ description: '推荐游戏列表', type: [RecommendedGameResponseDto] })
  recommendedGames: RecommendedGameResponseDto[];

  @ApiProperty({ description: '游戏分类组列表', type: [GameCategoryResponseDto] })
  gameCategories: GameCategoryResponseDto[];
}

// APP首页配置列表响应DTO
export class AppHomeConfigListResponseDto {
  @ApiProperty({ description: '配置ID', example: 1 })
  id: number;

  @ApiProperty({ description: '配置名称', example: '默认首页配置' })
  configName: string;

  @ApiProperty({ description: '配置描述', example: '系统默认的APP首页配置' })
  description: string;

  @ApiProperty({ description: '状态', example: 1 })
  status: number;

  @ApiProperty({ description: '排序', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '模板类型', example: 'box' })
  templateType: string;

  @ApiProperty({ description: '推荐游戏数量', example: 6 })
  recommendedGameCount: number;

  @ApiProperty({ description: '分类组数量', example: 4 })
  categoryCount: number;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createTime: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updateTime: Date;
}

// 分页响应DTO
export class AppHomeConfigPageResponseDto {
  @ApiProperty({ description: '数据列表', type: [AppHomeConfigListResponseDto] })
  list: AppHomeConfigListResponseDto[];

  @ApiProperty({ description: '总数', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
