import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Row,
  Col,
  Typography,
  InputNumber,
  ColorPicker,
  DatePicker,
  Switch,
  Modal,
  message,
  Tag,
  Tooltip,
  Upload,
  Image,
  Empty
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  UploadOutlined,
  InfoCircleOutlined,
  DragOutlined
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { Text, Title } = Typography;
const { RangePicker } = DatePicker;

// 浮点内容接口
interface FloatContent {
  id: string;
  type: string;
  title: string;
  content: string;
  imageUrl?: string;
  jumpType?: 'internal_route' | 'iframe_page';
  jumpTarget?: string;
  position: {
    x: number;
    y: number;
  };
  style?: {
    width?: number;
    height?: number;
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: number;
  };
  displayTime?: {
    startTime?: string;
    endTime?: string;
    duration?: number;
  };
  sortOrder: number;
  status: number;
}

// 组件属性接口
interface FloatContentConfigProps {
  value?: FloatContent[];
  onChange?: (contents: FloatContent[]) => void;
  disabled?: boolean;
  maxCount?: number;
}

// 内容类型选项
const CONTENT_TYPES = [
  { value: 'announcement', label: '系统公告', color: 'blue' },
  { value: 'promotion', label: '活动推广', color: 'green' },
  { value: 'customer_service', label: '客服咨询', color: 'orange' },
  { value: 'checkin_reminder', label: '签到提醒', color: 'purple' },
  { value: 'recharge_bonus', label: '充值优惠', color: 'red' }
];

// 跳转类型选项
const JUMP_TYPES = [
  { value: 'internal_route', label: '内部路由' },
  { value: 'iframe_page', label: 'iframe页面' }
];

// 浮点内容编辑模态框
const FloatContentModal: React.FC<{
  visible: boolean;
  onCancel: () => void;
  onOk: (content: FloatContent) => void;
  content?: FloatContent;
  mode: 'create' | 'edit';
}> = ({ visible, onCancel, onOk, content, mode }) => {
  const [form] = Form.useForm();
  const [previewVisible, setPreviewVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      if (mode === 'edit' && content) {
        form.setFieldsValue({
          ...content,
          displayTimeRange: content.displayTime?.startTime && content.displayTime?.endTime
            ? [dayjs(content.displayTime.startTime), dayjs(content.displayTime.endTime)]
            : undefined,
          backgroundColor: content.style?.backgroundColor,
          textColor: content.style?.textColor,
          width: content.style?.width,
          height: content.style?.height,
          borderRadius: content.style?.borderRadius,
          duration: content.displayTime?.duration
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          id: `float_${Date.now()}`,
          type: 'announcement',
          position: { x: 50, y: 20 },
          status: 1,
          sortOrder: 1
        });
      }
    }
  }, [visible, mode, content, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      const floatContent: FloatContent = {
        id: values.id,
        type: values.type,
        title: values.title,
        content: values.content,
        imageUrl: values.imageUrl,
        jumpType: values.jumpType,
        jumpTarget: values.jumpTarget,
        position: values.position,
        style: {
          width: values.width,
          height: values.height,
          backgroundColor: values.backgroundColor,
          textColor: values.textColor,
          borderRadius: values.borderRadius
        },
        displayTime: values.displayTimeRange ? {
          startTime: values.displayTimeRange[0].format('YYYY-MM-DD HH:mm:ss'),
          endTime: values.displayTimeRange[1].format('YYYY-MM-DD HH:mm:ss'),
          duration: values.duration
        } : {
          duration: values.duration
        },
        sortOrder: values.sortOrder,
        status: values.status ? 1 : 0
      };

      onOk(floatContent);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handlePreview = () => {
    const values = form.getFieldsValue();
    setPreviewVisible(true);
  };

  return (
    <>
      <Modal
        title={mode === 'create' ? '添加浮点内容' : '编辑浮点内容'}
        open={visible}
        onCancel={onCancel}
        onOk={handleOk}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: true,
            position: { x: 50, y: 20 },
            sortOrder: 1
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="id"
                label="唯一标识"
                rules={[{ required: true, message: '请输入唯一标识' }]}
              >
                <Input placeholder="如: float_1" disabled={mode === 'edit'} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="内容类型"
                rules={[{ required: true, message: '请选择内容类型' }]}
              >
                <Select placeholder="选择内容类型">
                  {CONTENT_TYPES.map(type => (
                    <Option key={type.value} value={type.value}>
                      <Tag color={type.color}>{type.label}</Tag>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="title"
            label="标题"
            rules={[
              { required: true, message: '请输入标题' },
              { max: 100, message: '标题长度不能超过100字符' }
            ]}
          >
            <Input placeholder="输入浮点内容标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[
              { required: true, message: '请输入内容' },
              { max: 500, message: '内容长度不能超过500字符' }
            ]}
          >
            <TextArea rows={3} placeholder="输入浮点内容详情" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="imageUrl" label="图片URL">
                <Input placeholder="输入图片URL（可选）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="jumpType" label="跳转类型">
                <Select placeholder="选择跳转类型（可选）" allowClear>
                  {JUMP_TYPES.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="jumpTarget" label="跳转目标">
            <Input placeholder="输入跳转目标路径或URL（可选）" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['position', 'x']}
                label="X坐标(%)"
                rules={[{ required: true, message: '请输入X坐标' }]}
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['position', 'y']}
                label="Y坐标(%)"
                rules={[{ required: true, message: '请输入Y坐标' }]}
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sortOrder"
                label="排序"
                rules={[{ required: true, message: '请输入排序' }]}
              >
                <InputNumber min={1} max={999} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name="width" label="宽度(px)">
                <InputNumber min={50} max={500} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="height" label="高度(px)">
                <InputNumber min={30} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="backgroundColor" label="背景色">
                <ColorPicker showText />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="textColor" label="文字颜色">
                <ColorPicker showText />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="borderRadius" label="圆角(px)">
                <InputNumber min={0} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="duration" label="显示时长(秒)">
                <InputNumber min={1} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="启用状态" valuePropName="checked">
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="displayTimeRange" label="显示时间范围">
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder={['开始时间', '结束时间']}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button icon={<EyeOutlined />} onClick={handlePreview}>
                预览效果
              </Button>
              <Text type="secondary">
                <InfoCircleOutlined /> 预览功能可以查看浮点内容的显示效果
              </Text>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title="浮点内容预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ 
          position: 'relative', 
          width: '100%', 
          height: 300, 
          backgroundColor: '#f0f0f0',
          border: '1px dashed #d9d9d9'
        }}>
          <Text type="secondary" style={{ position: 'absolute', top: 10, left: 10 }}>
            预览区域 (模拟APP界面)
          </Text>
          {/* 这里可以添加预览逻辑 */}
        </div>
      </Modal>
    </>
  );
};

// 主组件
const FloatContentConfig: React.FC<FloatContentConfigProps> = ({
  value = [],
  onChange,
  disabled = false,
  maxCount = 3
}) => {
  const [contents, setContents] = useState<FloatContent[]>(value);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingContent, setEditingContent] = useState<FloatContent | undefined>();
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    setContents(value);
  }, [value]);

  // 添加浮点内容
  const handleAdd = () => {
    if (contents.length >= maxCount) {
      message.warning(`最多只能添加${maxCount}个浮点内容`);
      return;
    }
    setEditingContent(undefined);
    setModalMode('create');
    setModalVisible(true);
  };

  // 编辑浮点内容
  const handleEdit = (content: FloatContent) => {
    setEditingContent(content);
    setModalMode('edit');
    setModalVisible(true);
  };

  // 删除浮点内容
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个浮点内容吗？',
      onOk: () => {
        const newContents = contents.filter(c => c.id !== id);
        setContents(newContents);
        onChange?.(newContents);
        message.success('删除成功');
      }
    });
  };

  // 保存浮点内容
  const handleSave = (content: FloatContent) => {
    let newContents: FloatContent[];

    if (modalMode === 'create') {
      newContents = [...contents, content];
    } else {
      newContents = contents.map(c => c.id === content.id ? content : c);
    }

    setContents(newContents);
    onChange?.(newContents);
    setModalVisible(false);
    message.success(modalMode === 'create' ? '添加成功' : '更新成功');
  };

  // 切换状态
  const handleToggleStatus = (id: string) => {
    const newContents = contents.map(content =>
      content.id === id
        ? { ...content, status: content.status === 1 ? 0 : 1 }
        : content
    );
    setContents(newContents);
    onChange?.(newContents);
  };

  // 拖拽排序
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const newContents = Array.from(contents);
    const [reorderedItem] = newContents.splice(result.source.index, 1);
    newContents.splice(result.destination.index, 0, reorderedItem);

    // 更新排序
    const reorderedContents = newContents.map((content, index) => ({
      ...content,
      sortOrder: index + 1
    }));

    setContents(reorderedContents);
    onChange?.(reorderedContents);
  };

  // 批量清空
  const handleClearAll = () => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有浮点内容吗？此操作不可恢复。',
      onOk: () => {
        setContents([]);
        onChange?.([]);
        message.success('已清空所有浮点内容');
      }
    });
  };

  // 获取内容类型标签
  const getTypeTag = (type: string) => {
    const typeConfig = CONTENT_TYPES.find(t => t.value === type);
    return typeConfig ? (
      <Tag color={typeConfig.color}>{typeConfig.label}</Tag>
    ) : (
      <Tag>{type}</Tag>
    );
  };

  return (
    <div>
      {/* 头部操作栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Title level={5} style={{ margin: 0 }}>
                <DragOutlined /> 浮点内容配置
              </Title>
              <Tag color={contents.length <= maxCount ? 'green' : 'red'}>
                {contents.length}/{maxCount} 个内容
              </Tag>
              {contents.length > maxCount && (
                <Tag color="red">超出限制</Tag>
              )}
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                disabled={disabled || contents.length >= maxCount}
              >
                添加浮点内容
              </Button>
              <Button
                danger
                size="small"
                onClick={handleClearAll}
                disabled={disabled || contents.length === 0}
              >
                清空全部
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 使用说明 */}
        <div style={{ marginTop: 12, padding: 8, backgroundColor: '#f6ffed', borderRadius: 4 }}>
          <Text type="secondary">
            <InfoCircleOutlined /> 浮点内容将在APP首页以浮层形式显示，支持定时显示、位置自定义等功能
          </Text>
        </div>
      </Card>

      {/* 浮点内容列表 */}
      {contents.length === 0 ? (
        <Card>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无浮点内容配置"
            style={{ margin: '40px 0' }}
          >
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加第一个浮点内容
            </Button>
          </Empty>
        </Card>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="float-contents">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {contents.map((content, index) => (
                  <Draggable
                    key={content.id}
                    draggableId={content.id}
                    index={index}
                    isDragDisabled={disabled}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        style={{
                          ...provided.draggableProps.style,
                          marginBottom: 16
                        }}
                      >
                        <Card
                          size="small"
                          title={
                            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                              <div {...provided.dragHandleProps}>
                                <DragOutlined style={{ color: '#999', cursor: 'grab' }} />
                              </div>
                              <span>{content.title}</span>
                              {getTypeTag(content.type)}
                              <Tag color={content.status === 1 ? 'green' : 'red'}>
                                {content.status === 1 ? '启用' : '禁用'}
                              </Tag>
                            </div>
                          }
                          extra={
                            <Space>
                              <Tooltip title="编辑">
                                <Button
                                  type="text"
                                  size="small"
                                  icon={<EditOutlined />}
                                  onClick={() => handleEdit(content)}
                                  disabled={disabled}
                                />
                              </Tooltip>
                              <Tooltip title={content.status === 1 ? '禁用' : '启用'}>
                                <Switch
                                  size="small"
                                  checked={content.status === 1}
                                  onChange={() => handleToggleStatus(content.id)}
                                  disabled={disabled}
                                />
                              </Tooltip>
                              <Tooltip title="删除">
                                <Button
                                  type="text"
                                  size="small"
                                  danger
                                  icon={<DeleteOutlined />}
                                  onClick={() => handleDelete(content.id)}
                                  disabled={disabled}
                                />
                              </Tooltip>
                            </Space>
                          }
                          style={{
                            backgroundColor: snapshot.isDragging ? '#f0f0f0' : '#fff'
                          }}
                        >
                          <Row gutter={16}>
                            <Col span={12}>
                              <div style={{ marginBottom: 8 }}>
                                <Text strong>内容：</Text>
                                <div style={{ marginTop: 4 }}>
                                  <Text>{content.content}</Text>
                                </div>
                              </div>

                              {content.imageUrl && (
                                <div style={{ marginBottom: 8 }}>
                                  <Text strong>图片：</Text>
                                  <div style={{ marginTop: 4 }}>
                                    <Image
                                      src={content.imageUrl}
                                      alt="浮点内容图片"
                                      width={60}
                                      height={40}
                                      style={{ objectFit: 'cover', borderRadius: 4 }}
                                    />
                                  </div>
                                </div>
                              )}

                              {content.jumpType && content.jumpTarget && (
                                <div style={{ marginBottom: 8 }}>
                                  <Text strong>跳转：</Text>
                                  <div style={{ marginTop: 4 }}>
                                    <Tag color="blue">
                                      {JUMP_TYPES.find(t => t.value === content.jumpType)?.label}
                                    </Tag>
                                    <Text type="secondary">{content.jumpTarget}</Text>
                                  </div>
                                </div>
                              )}
                            </Col>

                            <Col span={12}>
                              <div style={{ marginBottom: 8 }}>
                                <Text strong>位置：</Text>
                                <div style={{ marginTop: 4 }}>
                                  <Tag>X: {content.position.x}%</Tag>
                                  <Tag>Y: {content.position.y}%</Tag>
                                  <Tag>排序: {content.sortOrder}</Tag>
                                </div>
                              </div>

                              {content.style && (
                                <div style={{ marginBottom: 8 }}>
                                  <Text strong>样式：</Text>
                                  <div style={{ marginTop: 4 }}>
                                    {content.style.width && <Tag>宽: {content.style.width}px</Tag>}
                                    {content.style.height && <Tag>高: {content.style.height}px</Tag>}
                                    {content.style.backgroundColor && (
                                      <Tag color={content.style.backgroundColor}>
                                        背景色
                                      </Tag>
                                    )}
                                  </div>
                                </div>
                              )}

                              {content.displayTime && (
                                <div style={{ marginBottom: 8 }}>
                                  <Text strong>显示时间：</Text>
                                  <div style={{ marginTop: 4 }}>
                                    {content.displayTime.duration && (
                                      <Tag color="orange">时长: {content.displayTime.duration}秒</Tag>
                                    )}
                                    {content.displayTime.startTime && content.displayTime.endTime && (
                                      <div style={{ marginTop: 2 }}>
                                        <Text type="secondary" style={{ fontSize: 12 }}>
                                          {content.displayTime.startTime} ~ {content.displayTime.endTime}
                                        </Text>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </Col>
                          </Row>
                        </Card>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {/* 编辑模态框 */}
      <FloatContentModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleSave}
        content={editingContent}
        mode={modalMode}
      />
    </div>
  );
};

export default FloatContentConfig;
