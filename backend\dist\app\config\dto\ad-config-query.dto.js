"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdConfigListDto = exports.AdConfigQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const ad_config_entity_1 = require("../entities/ad-config.entity");
class AdConfigQueryDto {
    page = 1;
    pageSize = 10;
    adType;
    jumpType;
    status;
    title;
    adIdentifier;
}
exports.AdConfigQueryDto = AdConfigQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    __metadata("design:type", Number)
], AdConfigQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_validator_1.Max)(500, { message: '每页数量不能大于500' }),
    __metadata("design:type", Number)
], AdConfigQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '广告类型筛选：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏，7-首页4宫格',
        example: 1,
        required: false,
        enum: ad_config_entity_1.AdType
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '广告类型必须是数字' }),
    (0, class_validator_1.IsIn)([1, 2, 3, 4, 5, 6, 7], { message: '广告类型必须是1-7之间的数字' }),
    __metadata("design:type", Number)
], AdConfigQueryDto.prototype, "adType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '跳转类型筛选：1-内部路由，2-iframe页面',
        example: 1,
        required: false,
        enum: ad_config_entity_1.JumpType
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '跳转类型必须是数字' }),
    (0, class_validator_1.IsIn)([1, 2], { message: '跳转类型必须是1或2' }),
    __metadata("design:type", Number)
], AdConfigQueryDto.prototype, "jumpType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === null || value === undefined || value === '' ? undefined : parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], AdConfigQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标题搜索', example: '轮播', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '广告标题必须是字符串' }),
    __metadata("design:type", String)
], AdConfigQueryDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标识搜索', example: 'banner', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '广告标识必须是字符串' }),
    __metadata("design:type", String)
], AdConfigQueryDto.prototype, "adIdentifier", void 0);
class AdConfigListDto {
    id;
    adIdentifier;
    adType;
    adTypeName;
    title;
    images;
    jumpType;
    jumpTypeName;
    jumpTarget;
    sortOrder;
    status;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
}
exports.AdConfigListDto = AdConfigListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告配置ID' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标识' }),
    __metadata("design:type", String)
], AdConfigListDto.prototype, "adIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告类型' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "adType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告类型名称' }),
    __metadata("design:type", String)
], AdConfigListDto.prototype, "adTypeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标题' }),
    __metadata("design:type", String)
], AdConfigListDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL数组' }),
    __metadata("design:type", Array)
], AdConfigListDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '跳转类型' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "jumpType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '跳转类型名称' }),
    __metadata("design:type", String)
], AdConfigListDto.prototype, "jumpTypeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '跳转目标' }),
    __metadata("design:type", String)
], AdConfigListDto.prototype, "jumpTarget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注说明' }),
    __metadata("design:type", String)
], AdConfigListDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人ID' }),
    __metadata("design:type", Number)
], AdConfigListDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], AdConfigListDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], AdConfigListDto.prototype, "updateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人信息', required: false }),
    __metadata("design:type", Object)
], AdConfigListDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人信息', required: false }),
    __metadata("design:type", Object)
], AdConfigListDto.prototype, "updater", void 0);
//# sourceMappingURL=ad-config-query.dto.js.map