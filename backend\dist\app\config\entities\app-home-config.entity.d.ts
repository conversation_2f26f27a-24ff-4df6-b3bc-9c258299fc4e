import { SysUser } from '../../../system/entities/sys-user.entity';
import { AdConfig } from './ad-config.entity';
import { AppHomeRecommendedGame } from './app-home-recommended-game.entity';
import { AppHomeGameCategory } from './app-home-game-category.entity';
export interface FloatContent {
    id: string;
    type: string;
    title: string;
    content: string;
    imageUrl?: string;
    jumpType?: 'internal_route' | 'iframe_page';
    jumpTarget?: string;
    position: {
        x: number;
        y: number;
    };
    style?: {
        width?: number;
        height?: number;
        backgroundColor?: string;
        textColor?: string;
        borderRadius?: number;
    };
    displayTime?: {
        startTime?: string;
        endTime?: string;
        duration?: number;
    };
    sortOrder: number;
    status: number;
}
export declare class AppHomeConfig {
    id: number;
    configName: string;
    description: string;
    topFloatAdId: number;
    carouselAdId: number;
    homeGridAdId: number;
    splashPopupAdId: number;
    floatAdId: number;
    templateType: string;
    floatContents: FloatContent[];
    status: number;
    sortOrder: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    topFloatAd: AdConfig;
    carouselAd: AdConfig;
    homeGridAd: AdConfig;
    splashPopupAd: AdConfig;
    floatAd: AdConfig;
    recommendedGames: AppHomeRecommendedGame[];
    gameCategories: AppHomeGameCategory[];
    isEnabled(): boolean;
    hasRequiredAds(): boolean;
    getAdCount(): number;
    validateConfiguration(): {
        valid: boolean;
        errors: string[];
    };
    isBoxMode(): boolean;
    isClassicMode(): boolean;
    getFloatContentCount(): number;
    getActiveFloatContents(): FloatContent[];
    validateBoxModeConfig(): {
        valid: boolean;
        errors: string[];
    };
}
