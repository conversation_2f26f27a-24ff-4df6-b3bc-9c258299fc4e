const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库配置
const pool = new Pool({
  host: '**************',
  port: 5435,
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
  database: 'inapp2',
});

async function runMigration() {
  try {
    // 读取迁移文件
    const migrationPath = path.join(__dirname, 'database/migrations/015-add-float-ad-to-app-home-config.sql');
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('执行迁移: 015-add-float-ad-to-app-home-config.sql');
    console.log('SQL内容:');
    console.log(sql);
    
    // 执行迁移
    await pool.query(sql);
    
    console.log('✅ 迁移执行成功！');
  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await pool.end();
  }
}

runMigration();
