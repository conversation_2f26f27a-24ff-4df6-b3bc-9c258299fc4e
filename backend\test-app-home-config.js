const BASE_URL = 'http://localhost:3000/api';

async function testAppHomeConfig() {
  try {
    // 1. 登录获取token
    console.log('🔐 正在登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (loginData.code !== 200) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const token = loginData.result.token;
    console.log('✅ 登录成功');

    // 设置请求头
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 测试获取APP首页配置列表
    console.log('\n📋 测试获取APP首页配置列表...');
    const listResponse = await fetch(`${BASE_URL}/config/app-home`, { headers });
    const listData = await listResponse.json();

    console.log('📊 列表响应状态:', listResponse.status);
    console.log('📋 列表响应数据:', JSON.stringify(listData, null, 2));

    // 3. 测试获取广告配置（按类型）
    console.log('\n📋 测试获取Banner广告配置...');
    const bannerAdsResponse = await fetch(`${BASE_URL}/config/ads/by-type/5`, { headers });
    const bannerAdsData = await bannerAdsResponse.json();

    console.log('📊 Banner广告响应状态:', bannerAdsResponse.status);
    console.log('📋 Banner广告数据:', JSON.stringify(bannerAdsData, null, 2));

    // 4. 测试获取应用列表
    console.log('\n📋 测试获取应用列表...');
    const appsResponse = await fetch(`${BASE_URL}/applications?pageSize=50&status=active`, { headers });
    const appsData = await appsResponse.json();

    console.log('📊 应用列表响应状态:', appsResponse.status);
    console.log('📋 应用列表数据:', JSON.stringify(appsData, null, 2));

    // 5. 检查游戏数据结构
    if (appsData.code === 200 && appsData.result.list.length > 0) {
      console.log('\n🎮 第一个游戏的数据结构:');
      const firstGame = appsData.result.list[0];
      console.log('- ID:', firstGame.id);
      console.log('- Name:', firstGame.name);
      console.log('- Categories:', firstGame.categories);
      console.log('- Category L1:', firstGame.categoryL1Value);
      console.log('- Category L2:', firstGame.categoryL2Value);
      console.log('- IconUrl:', firstGame.iconUrl);
      console.log('- Status:', firstGame.status);
    }

    console.log('\n✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testAppHomeConfig();
