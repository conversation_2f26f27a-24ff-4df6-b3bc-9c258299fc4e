import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Form, 
  Input, 
  Select, 
  InputNumber, 
  message, 
  Space, 
  Tag, 
  Modal, 
  Switch,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Divider,
  Typography
} from 'antd';
import { 
  HomeOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { BasicContent } from '#src/components';
import {
  fetchAppHomeConfigs,
  fetchAppHomeConfig,
  fetchCreateAppHomeConfig,
  fetchUpdateAppHomeConfig,
  fetchDeleteAppHomeConfig,
  fetchToggleAppHomeConfigStatus,
  fetchAdConfigs,
  type AppHomeConfig,
  type AppHomeConfigListItem,
  type AppHomeConfigQueryParams,
  type AppHomeConfigFormData,
  type AdConfig
} from '#src/api/config';
import { fetchApplications, type Application } from '#src/api/application';
import BoxModeForm from './components/BoxModeForm';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

// 状态选项
const STATUS_OPTIONS = [
  { value: 1, label: '启用', color: 'green' },
  { value: 0, label: '禁用', color: 'red' },
];

const AppHomeConfigPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<AppHomeConfigListItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchForm] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingRecord, setEditingRecord] = useState<AppHomeConfigListItem | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailData, setDetailData] = useState<AppHomeConfig | null>(null);

  // 选项数据
  const [adOptions, setAdOptions] = useState<AdConfig[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);

  // 加载数据
  const loadData = async (params?: AppHomeConfigQueryParams) => {
    try {
      setLoading(true);
      const searchValues = searchForm.getFieldsValue();
      const queryParams = {
        page: currentPage,
        pageSize,
        ...searchValues,
        ...params,
      };

      const response = await fetchAppHomeConfigs(queryParams);
      if (response.code === 200) {
        setDataSource(response.result.list);
        setTotal(response.result.total);
      } else {
        message.error(response.message || '获取数据失败');
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载选项数据
  const loadOptions = async () => {
    try {
      // 加载广告配置选项
      const adResponse = await fetchAdConfigs({ pageSize: 1000, status: 1 });
      if (adResponse.code === 200) {
        setAdOptions(adResponse.result.list);
      }

      // 加载游戏应用选项
      const gameResponse = await fetchApplications({ pageSize: 1000, status: 'active' });
      if (gameResponse.code === 200) {
        setApplications(gameResponse.result.list);
      }
    } catch (error) {
      console.error('加载选项数据失败:', error);
    }
  };

  useEffect(() => {
    loadData();
    loadOptions();
  }, [currentPage, pageSize]);

  // 搜索
  const handleSearch = () => {
    setCurrentPage(1);
    loadData();
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    loadData();
  };

  // 打开创建/编辑弹窗
  const handleOpenModal = async (type: 'create' | 'edit', record?: AppHomeConfigListItem) => {
    setModalType(type);
    setEditingRecord(record || null);
    setModalVisible(true);

    if (type === 'edit' && record) {
      try {
        // 获取详细配置数据
        const response = await fetchAppHomeConfig(record.id);
        if (response.code === 200) {
          const detailData = response.result;
          modalForm.setFieldsValue({
            configName: detailData.configName,
            description: detailData.description,
            templateType: detailData.templateType || 'box',
            status: detailData.status,
            sortOrder: detailData.sortOrder,
            remark: detailData.remark,
            topFloatAdId: detailData.topFloatAdId,
            carouselAdId: detailData.carouselAdId,
            homeGridAdId: detailData.homeGridAdId,
            splashPopupAdId: detailData.splashPopupAdId,
            floatAdId: detailData.floatAdId,
            floatContents: detailData.floatContents || [],
            gameCategories: detailData.gameCategories || [],
          });
        } else {
          message.error('获取配置详情失败');
        }
      } catch (error) {
        console.error('获取配置详情失败:', error);
        message.error('获取配置详情失败');
      }
    } else {
      modalForm.resetFields();
      modalForm.setFieldsValue({
        templateType: 'box',
        status: 1,
        sortOrder: 1,
        gameCategories: [],
        floatContents: []
      });
    }
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setModalVisible(false);
    setEditingRecord(null);
    modalForm.resetFields();
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await modalForm.validateFields();
      setLoading(true);

      if (modalType === 'create') {
        const response = await fetchCreateAppHomeConfig(values);
        if (response.code === 200) {
          message.success('创建成功');
          handleCloseModal();
          loadData();
        } else {
          message.error(response.message || '创建失败');
        }
      } else if (editingRecord) {
        const response = await fetchUpdateAppHomeConfig(editingRecord.id, values);
        if (response.code === 200) {
          message.success('更新成功');
          handleCloseModal();
          loadData();
        } else {
          message.error(response.message || '更新失败');
        }
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换状态
  const handleToggleStatus = async (record: AppHomeConfigListItem) => {
    try {
      const response = await fetchToggleAppHomeConfigStatus(record.id);
      if (response.code === 200) {
        message.success(`${record.status === 1 ? '禁用' : '启用'}成功`);
        loadData();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('切换状态失败:', error);
      message.error('操作失败');
    }
  };

  // 删除
  const handleDelete = async (record: AppHomeConfigListItem) => {
    try {
      const response = await fetchDeleteAppHomeConfig(record.id);
      if (response.code === 200) {
        message.success('删除成功');
        loadData();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 查看详情
  const handleViewDetail = async (record: AppHomeConfigListItem) => {
    try {
      setLoading(true);
      const response = await fetchAppHomeConfig(record.id);
      if (response.code === 200) {
        setDetailData(response.result);
        setDetailModalVisible(true);
      } else {
        message.error(response.message || '获取详情失败');
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '配置名称',
      dataIndex: 'configName',
      key: 'configName',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '模板类型',
      dataIndex: 'templateType',
      key: 'templateType',
      width: 100,
      render: (templateType: string) => (
        <Tag color={templateType === 'box' ? 'blue' : 'default'}>
          {templateType === 'box' ? 'BOX' : '经典'}
        </Tag>
      ),
    },
    {
      title: '推荐游戏数',
      dataIndex: 'recommendedGameCount',
      key: 'recommendedGameCount',
      width: 120,
      render: (count: number) => (
        <Tag color="blue">{count}个</Tag>
      ),
    },
    {
      title: '分类组数',
      dataIndex: 'categoryCount',
      key: 'categoryCount',
      width: 100,
      render: (count: number) => (
        <Tag color="green">{count}个</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number) => {
        const option = STATUS_OPTIONS.find(opt => opt.value === status);
        return <Tag color={option?.color}>{option?.label}</Tag>;
      },
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: AppHomeConfigListItem) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              size="small" 
              icon={<EditOutlined />}
              onClick={() => handleOpenModal('edit', record)}
            />
          </Tooltip>
          <Tooltip title={record.status === 1 ? '禁用' : '启用'}>
            <Switch
              size="small"
              checked={record.status === 1}
              onChange={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                size="small" 
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <BasicContent>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={24}>
              <Title level={4}>
                <HomeOutlined style={{ marginRight: 8 }} />
                APP首页配置管理
              </Title>
              <Text type="secondary">
                管理APP首页展示的广告位、推荐游戏和分类组配置
              </Text>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="inline"
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="configName" label="配置名称">
            <Input placeholder="请输入配置名称" allowClear />
          </Form.Item>
          <Form.Item name="status" label="状态">
            <Select placeholder="请选择状态" allowClear style={{ width: 120 }}>
              {STATUS_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => handleOpenModal('create')}
              >
                新增配置
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={() => loadData()}
              >
                刷新
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
        />
      </Card>

      {/* 创建/编辑弹窗 */}
      <Modal
        title={modalType === 'create' ? '新增APP首页配置 (BOX模式)' : '编辑APP首页配置 (BOX模式)'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCloseModal}
        width={1200}
        confirmLoading={loading}
        destroyOnClose
        style={{ top: 20 }}
      >
        <Form
          form={modalForm}
          layout="vertical"
          preserve={false}
          initialValues={{
            templateType: 'box',
            status: 1,
            sortOrder: 1,
            gameCategories: [],
            floatContents: []
          }}
        >
          <BoxModeForm
            form={modalForm}
            adOptions={adOptions}
            applications={applications}
            disabled={loading}
          />
        </Form>
      </Modal>

      {/* 详情弹窗 */}
      <Modal
        title="APP首页配置详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        {detailData && (
          <div>
            <Title level={5}>基本信息</Title>
            <Row gutter={16}>
              <Col span={6}>
                <Text strong>配置名称：</Text>
                <Text>{detailData.configName}</Text>
              </Col>
              <Col span={6}>
                <Text strong>模板类型：</Text>
                <Tag color={detailData.templateType === 'box' ? 'blue' : 'default'}>
                  {detailData.templateType === 'box' ? 'BOX模式' : '经典模式'}
                </Tag>
              </Col>
              <Col span={6}>
                <Text strong>状态：</Text>
                <Tag color={detailData.status === 1 ? 'green' : 'red'}>
                  {detailData.status === 1 ? '启用' : '禁用'}
                </Tag>
              </Col>
              <Col span={6}>
                <Text strong>排序：</Text>
                <Text>{detailData.sortOrder}</Text>
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <Text strong>描述：</Text>
              <Text>{detailData.description || '-'}</Text>
            </div>

            <Divider />

            <Title level={5}>广告配置</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>顶部浮动广告：</Text>
                <Text>{detailData.topFloatAd?.title || '-'}</Text>
              </Col>
              <Col span={12}>
                <Text strong>轮播广告：</Text>
                <Text>{detailData.carouselAd?.title || '-'}</Text>
              </Col>
            </Row>

            {detailData.templateType === 'box' && (
              <Row gutter={16} style={{ marginTop: 12 }}>
                <Col span={24}>
                  <Text strong>6宫格广告：</Text>
                  <Text>{detailData.homeGridAd?.title || '-'}</Text>
                  {!detailData.homeGridAd && (
                    <Tag color="red" style={{ marginLeft: 8 }}>未配置（BOX模式必需）</Tag>
                  )}
                </Col>
              </Row>
            )}

            <Row gutter={16} style={{ marginTop: 12 }}>
              <Col span={12}>
                <Text strong>开屏弹窗广告：</Text>
                <Text>{detailData.splashPopupAd?.title || '-'}</Text>
              </Col>
              <Col span={12}>
                <Text strong>浮点广告：</Text>
                <Text>{detailData.floatAd?.title || '-'}</Text>
              </Col>
            </Row>

            {/* BOX模式特有配置 */}
            {detailData.templateType === 'box' && (
              <>
                <Divider />

                <Title level={5}>浮点内容配置 ({detailData.floatContents?.length || 0}个)</Title>
                {detailData.floatContents && detailData.floatContents.length > 0 ? (
                  detailData.floatContents.map((content: any, index: number) => (
                    <div key={index} style={{ marginBottom: 12, padding: 12, border: '1px solid #d9d9d9', borderRadius: 4 }}>
                      <Row gutter={16}>
                        <Col span={8}>
                          <Text strong>标题：</Text>
                          <Text>{content.title}</Text>
                        </Col>
                        <Col span={8}>
                          <Text strong>类型：</Text>
                          <Tag>{content.type}</Tag>
                        </Col>
                        <Col span={8}>
                          <Text strong>跳转类型：</Text>
                          <Tag color={content.jumpType === 'route' ? 'blue' : 'green'}>
                            {content.jumpType === 'route' ? '路由' : '内嵌页面'}
                          </Tag>
                        </Col>
                      </Row>
                      <div style={{ marginTop: 8 }}>
                        <Text strong>内容：</Text>
                        <Text>{content.content}</Text>
                      </div>
                    </div>
                  ))
                ) : (
                  <Text type="secondary">暂无浮点内容配置</Text>
                )}
              </>
            )}

            <Divider />

            <Title level={5}>推荐游戏 ({detailData.recommendedGames.length}个)</Title>
            {detailData.recommendedGames.map((game, index) => (
              <div key={game.id} style={{ marginBottom: 8 }}>
                <Text>{index + 1}. {game.game.name}</Text>
              </div>
            ))}

            <Divider />

            <Title level={5}>游戏分类组 ({detailData.gameCategories.length}个)</Title>
            {detailData.gameCategories.map((category, index) => (
              <div key={category.id} style={{ marginBottom: 16 }}>
                <Text strong>
                  {index + 1}. {category.categoryTitle['zh-CN']} ({category.games.length}个游戏)
                </Text>
                <div style={{ marginLeft: 16, marginTop: 4 }}>
                  {category.games.map((game, gameIndex) => (
                    <div key={game.id}>
                      <Text type="secondary">
                        {gameIndex + 1}. {game.game.name}
                      </Text>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </Modal>
    </BasicContent>
  );
};

export default AppHomeConfigPage;
