/**
 * Supabase图片上传工具 - 使用原生Storage API
 * 避免AWS SDK在浏览器中的兼容性问题
 */

import { createClient } from '@supabase/supabase-js';
import { uploadConfig, configValidation } from './upload-config';
import type { UploadResult, CompressionOptions, ProgressCallback } from './supabase-upload.types';

// 验证配置
if (!configValidation.valid) {
  console.error('Upload configuration is invalid:', configValidation.errors);
  throw new Error('Upload configuration validation failed');
}

// 创建Supabase客户端
const supabase = createClient(uploadConfig.supabaseUrl, uploadConfig.supabaseAnonKey);

/**
 * 压缩图片
 */
function compressImage(
  file: File,
  maxWidth: number,
  maxHeight: number,
  quality: number
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img;

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 使用Supabase Storage API上传图片
 */
export async function uploadImageToSupabase(file: File, folder: string = uploadConfig.s3.folder): Promise<string> {
  try {
    // 验证文件类型
    if (!uploadConfig.upload.allowedTypes.includes(file.type)) {
      throw new Error(`只能上传以下格式的图片: ${uploadConfig.upload.allowedTypes.join(', ')}`);
    }

    // 验证文件大小
    if (file.size > uploadConfig.upload.maxFileSize) {
      const maxSizeMB = (uploadConfig.upload.maxFileSize / 1024 / 1024).toFixed(1);
      throw new Error(`图片大小不能超过${maxSizeMB}MB`);
    }

    // 生成唯一文件名
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = `${folder}/${fileName}`;

    // 使用Supabase Storage API上传
    const { data, error } = await supabase.storage
      .from(uploadConfig.s3.bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Supabase上传错误:', error);
      throw new Error(`上传失败: ${error.message}`);
    }

    if (!data?.path) {
      throw new Error('上传失败，未获取到文件路径');
    }

    // 构建公共URL
    const { data: urlData } = supabase.storage
      .from(uploadConfig.s3.bucket)
      .getPublicUrl(data.path);

    if (!urlData?.publicUrl) {
      throw new Error('获取公共URL失败');
    }

    console.log('图片上传成功:', urlData.publicUrl);
    return urlData.publicUrl;

  } catch (error) {
    console.error('上传图片失败:', error);
    throw error;
  }
}

/**
 * 删除图片
 */
export async function deleteImageFromSupabase(imageUrl: string, folder: string = uploadConfig.s3.folder): Promise<boolean> {
  try {
    // 从URL中提取文件路径
    const url = new URL(imageUrl);
    const pathParts = url.pathname.split('/');

    // 提取文件路径
    let filePath = '';
    const publicIndex = pathParts.indexOf('public');
    if (publicIndex !== -1 && pathParts[publicIndex + 1] === uploadConfig.s3.bucket) {
      // URL格式: .../storage/v1/object/public/{bucket}/{folder}/filename.jpg
      filePath = pathParts.slice(publicIndex + 2).join('/');
    } else {
      // 备用方案：假设是文件名
      const fileName = pathParts[pathParts.length - 1];
      filePath = `${folder}/${fileName}`;
    }

    // 使用Supabase Storage API删除
    const { error } = await supabase.storage
      .from(uploadConfig.s3.bucket)
      .remove([filePath]);

    if (error) {
      console.error('删除文件失败:', error);
      throw new Error(`删除失败: ${error.message}`);
    }

    console.log('文件删除成功:', filePath);
    return true;

  } catch (error) {
    console.error('删除图片失败:', error);
    return false;
  }
}

/**
 * 批量上传图片到Supabase存储
 * @param files 要上传的文件数组
 * @param folder 存储文件夹名称
 * @param onProgress 上传进度回调
 * @returns 返回上传结果数组
 */
export async function uploadMultipleImagesToSupabase(
  files: File[],
  folder: string = uploadConfig.s3.folder,
  onProgress?: ProgressCallback
): Promise<UploadResult[]> {
  const results: UploadResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    try {
      const url = await uploadImageToSupabase(file, folder);
      results.push({ success: true, url, fileName: file.name });
    } catch (error) {
      results.push({ 
        success: false, 
        error: error instanceof Error ? error.message : '上传失败', 
        fileName: file.name 
      });
    }
    
    // 调用进度回调
    if (onProgress) {
      const progress = ((i + 1) / files.length) * 100;
      onProgress(progress, i + 1, files.length);
    }
  }
  
  return results;
}



/**
 * 压缩并上传图片
 */
export async function uploadCompressedImageToSupabase(
  file: File,
  folder: string = uploadConfig.s3.folder,
  options: CompressionOptions = {}
): Promise<string> {
  const {
    maxWidth = uploadConfig.upload.compressionOptions.maxWidth,
    maxHeight = uploadConfig.upload.compressionOptions.maxHeight,
    quality = uploadConfig.upload.compressionOptions.quality
  } = options;

  try {
    // 压缩图片
    const compressedFile = await compressImage(file, maxWidth, maxHeight, quality);

    // 上传压缩后的图片
    return await uploadImageToSupabase(compressedFile, folder);
  } catch (error) {
    console.error('压缩上传失败:', error);
    throw error;
  }
}
