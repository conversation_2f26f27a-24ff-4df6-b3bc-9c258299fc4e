!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e=e||self).useMemoOne={},e.React)}(this,function(e,o){"use strict";function n(e,t){var n=o.useState(function(){return{inputs:t,result:e()}})[0],u=o.useRef(!0),r=o.useRef(n),f=u.current||Boolean(t&&r.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,r.current.inputs))?r.current:{inputs:t,result:e()};return o.useEffect(function(){u.current=!1,r.current=f},[f]),f.result}function t(e,t){return n(function(){return e},t)}var u=n,r=t;e.useCallback=r,e.useCallbackOne=t,e.useMemo=u,e.useMemoOne=n,Object.defineProperty(e,"__esModule",{value:!0})});
