const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  const client = new Client({
    host: '**************',
    port: 5435,
    user: 'user_jJSpPW',
    password: 'password_DmrhYX',
    database: 'inapp2',
  });

  try {
    await client.connect();
    console.log('✅ 连接到数据库成功');

    // 读取迁移文件
    const migrationPath = path.join(__dirname, 'migrations', '016-add-application-boolean-fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('🔄 执行 applications 表字段添加迁移...');
    await client.query(migrationSQL);
    console.log('✅ 迁移执行成功');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

runMigration();
