"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfig = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
const ad_config_entity_1 = require("./ad-config.entity");
const app_home_recommended_game_entity_1 = require("./app-home-recommended-game.entity");
const app_home_game_category_entity_1 = require("./app-home-game-category.entity");
let AppHomeConfig = class AppHomeConfig {
    id;
    configName;
    description;
    topBannerAdId;
    carouselAdId;
    homeGridAdId;
    splashPopupAdId;
    floatAdId;
    templateType;
    floatContents;
    status;
    sortOrder;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    topBannerAd;
    carouselAd;
    homeGridAd;
    splashPopupAd;
    floatAd;
    recommendedGames;
    gameCategories;
    isEnabled() {
        return this.status === 1;
    }
    hasRequiredAds() {
        if (this.templateType === 'box') {
            return !!(this.topBannerAdId && this.carouselAdId);
        }
        return !!(this.topBannerAdId && this.carouselAdId);
    }
    getAdCount() {
        let count = 0;
        if (this.topBannerAdId)
            count++;
        if (this.carouselAdId)
            count++;
        if (this.homeGridAdId)
            count++;
        if (this.splashPopupAdId)
            count++;
        if (this.floatAdId)
            count++;
        return count;
    }
    validateConfiguration() {
        const errors = [];
        if (!this.configName?.trim()) {
            errors.push('配置名称不能为空');
        }
        if (!this.topBannerAdId) {
            errors.push('顶部Banner广告为必填项');
        }
        if (!this.carouselAdId) {
            errors.push('轮播广告为必填项');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    isBoxMode() {
        return this.templateType === 'box';
    }
    isClassicMode() {
        return this.templateType === 'classic';
    }
    getFloatContentCount() {
        return this.floatContents?.length || 0;
    }
    getActiveFloatContents() {
        return this.floatContents?.filter(content => content.status === 1) || [];
    }
    validateBoxModeConfig() {
        const errors = [];
        if (this.templateType === 'box') {
            if (!this.homeGridAdId) {
                errors.push('BOX模式需要配置6宫格广告');
            }
            if (!this.gameCategories || this.gameCategories.length < 4) {
                errors.push('BOX模式至少需要4个游戏分类组');
            }
            if (this.gameCategories) {
                this.gameCategories.forEach((category, index) => {
                    if (!category.categoryGames || category.categoryGames.length < 4) {
                        errors.push(`第${index + 1}个游戏分类至少需要4个游戏`);
                    }
                });
            }
            if (this.floatContents && this.floatContents.length > 3) {
                errors.push('BOX模式浮点内容最多3个');
            }
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
};
exports.AppHomeConfig = AppHomeConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'config_name', length: 100, comment: '配置名称' }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "configName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '配置描述' }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'top_banner_ad_id', nullable: true, comment: '顶部Banner广告ID（必填）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "topBannerAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'carousel_ad_id', nullable: true, comment: '轮播广告ID（必填）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "carouselAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'home_grid_ad_id', nullable: true, comment: '首页九宫格ID（可选）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "homeGridAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'splash_popup_ad_id', nullable: true, comment: '开屏弹窗广告ID（可选）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "splashPopupAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'float_ad_id', nullable: true, comment: '浮点广告ID（可选）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "floatAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'template_type',
        length: 50,
        default: 'box',
        comment: '首页模板类型：box-BOX模式，classic-经典模式，card-卡片模式，waterfall-瀑布流模式'
    }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "templateType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'float_contents',
        type: 'jsonb',
        default: () => "'[]'",
        comment: '浮点内容配置数组，包含公告、活动、客服等浮点内容'
    }),
    __metadata("design:type", Array)
], AppHomeConfig.prototype, "floatContents", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sort_order', default: 0, comment: '排序，数字越小越靠前' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '备注说明' }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppHomeConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppHomeConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeConfig.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'top_banner_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "topBannerAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'carousel_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "carouselAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'home_grid_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "homeGridAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'splash_popup_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "splashPopupAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'float_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "floatAd", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_home_recommended_game_entity_1.AppHomeRecommendedGame, (recommendedGame) => recommendedGame.homeConfig),
    __metadata("design:type", Array)
], AppHomeConfig.prototype, "recommendedGames", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_home_game_category_entity_1.AppHomeGameCategory, (gameCategory) => gameCategory.homeConfig),
    __metadata("design:type", Array)
], AppHomeConfig.prototype, "gameCategories", void 0);
exports.AppHomeConfig = AppHomeConfig = __decorate([
    (0, typeorm_1.Entity)('app_home_configs')
], AppHomeConfig);
//# sourceMappingURL=app-home-config.entity.js.map