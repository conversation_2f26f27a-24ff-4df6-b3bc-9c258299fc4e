// 测试BOX模式API的简单脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试数据
const testData = {
  configName: 'BOX模式测试配置',
  description: 'BOX模式功能测试',
  templateType: 'box',
  topFloatAdId: 1,
  carouselAdId: 2,
  homeGridAdId: 3, // BOX模式必需
  splashPopupAdId: 4,
  floatAdId: 5,
  floatContents: [
    {
      id: 'float-1',
      type: 'announcement',
      title: '系统公告',
      content: '欢迎使用BOX模式首页配置',
      position: { x: 50, y: 20 },
      size: { width: 300, height: 100 },
      style: {
        backgroundColor: '#1890ff',
        textColor: '#ffffff',
        borderRadius: 8,
        opacity: 0.9
      },
      jumpType: 'route',
      jumpTarget: '/announcement',
      isVisible: true,
      sortOrder: 1
    }
  ],
  gameCategories: [
    {
      id: 'cat-1',
      titleZh: '热门游戏',
      titleEn: 'Popular Games',
      sortOrder: 1,
      games: [
        { applicationId: 1, sortOrder: 1 },
        { applicationId: 2, sortOrder: 2 },
        { applicationId: 3, sortOrder: 3 },
        { applicationId: 4, sortOrder: 4 }
      ]
    },
    {
      id: 'cat-2',
      titleZh: '推荐游戏',
      titleEn: 'Recommended Games',
      sortOrder: 2,
      games: [
        { applicationId: 5, sortOrder: 1 },
        { applicationId: 6, sortOrder: 2 },
        { applicationId: 7, sortOrder: 3 },
        { applicationId: 8, sortOrder: 4 }
      ]
    },
    {
      id: 'cat-3',
      titleZh: '新游戏',
      titleEn: 'New Games',
      sortOrder: 3,
      games: [
        { applicationId: 9, sortOrder: 1 },
        { applicationId: 10, sortOrder: 2 },
        { applicationId: 11, sortOrder: 3 },
        { applicationId: 12, sortOrder: 4 }
      ]
    },
    {
      id: 'cat-4',
      titleZh: '经典游戏',
      titleEn: 'Classic Games',
      sortOrder: 4,
      games: [
        { applicationId: 13, sortOrder: 1 },
        { applicationId: 14, sortOrder: 2 },
        { applicationId: 15, sortOrder: 3 },
        { applicationId: 16, sortOrder: 4 }
      ]
    }
  ],
  recommendedGames: [
    { applicationId: 1, sortOrder: 1 },
    { applicationId: 2, sortOrder: 2 },
    { applicationId: 3, sortOrder: 3 },
    { applicationId: 4, sortOrder: 4 },
    { applicationId: 5, sortOrder: 5 },
    { applicationId: 6, sortOrder: 6 }
  ],
  sortOrder: 1,
  status: 1,
  remark: 'BOX模式测试配置'
};

async function testBoxModeAPI() {
  try {
    console.log('🚀 开始测试BOX模式API...');
    
    // 1. 测试创建BOX模式配置
    console.log('\n📝 测试创建BOX模式配置...');
    const createResponse = await axios.post(`${BASE_URL}/config/app-home`, testData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (createResponse.status === 201) {
      console.log('✅ 创建成功:', createResponse.data);
      const configId = createResponse.data.result.id;
      
      // 2. 测试获取配置详情
      console.log('\n📖 测试获取配置详情...');
      const getResponse = await axios.get(`${BASE_URL}/config/app-home/${configId}`);
      
      if (getResponse.status === 200) {
        console.log('✅ 获取详情成功');
        console.log('模板类型:', getResponse.data.result.templateType);
        console.log('浮点内容数量:', getResponse.data.result.floatContents?.length || 0);
        console.log('游戏分类数量:', getResponse.data.result.gameCategories?.length || 0);
      }
      
      // 3. 测试获取配置列表
      console.log('\n📋 测试获取配置列表...');
      const listResponse = await axios.get(`${BASE_URL}/config/app-home`);
      
      if (listResponse.status === 200) {
        console.log('✅ 获取列表成功');
        console.log('配置数量:', listResponse.data.result.data.length);
      }
      
    } else {
      console.log('❌ 创建失败:', createResponse.data);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testBoxModeAPI();
