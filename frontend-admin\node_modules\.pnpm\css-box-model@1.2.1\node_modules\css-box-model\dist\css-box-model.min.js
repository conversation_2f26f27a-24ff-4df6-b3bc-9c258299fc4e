!function(t,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports):"function"==typeof define&&define.amd?define(["exports"],o):o((t=t||self).cssBoxModel={})}(this,function(t){"use strict";var o="Invariant failed";var r=function(t){var o=t.top,r=t.right,e=t.bottom,i=t.left;return{top:o,right:r,bottom:e,left:i,width:r-i,height:e-o,x:i,y:o,center:{x:(r+i)/2,y:(e+o)/2}}},e=function(t,o){return{top:t.top-o.top,left:t.left-o.left,bottom:t.bottom+o.bottom,right:t.right+o.right}},i=function(t,o){return{top:t.top+o.top,left:t.left+o.left,bottom:t.bottom-o.bottom,right:t.right-o.right}},n={top:0,right:0,bottom:0,left:0},d=function(t){var o=t.borderBox,d=t.margin,f=void 0===d?n:d,g=t.border,a=void 0===g?n:g,p=t.padding,u=void 0===p?n:p,b=r(e(o,f)),m=r(i(o,a)),h=r(i(m,u));return{marginBox:b,borderBox:r(o),paddingBox:m,contentBox:h,margin:f,border:a,padding:u}},f=function(t){var r=t.slice(0,-2);if("px"!==t.slice(-2))return 0;var e=Number(r);return isNaN(e)&&function(t,r){if(!t)throw new Error(o)}(!1),e},g=function(t,o){var r,e,i=t.borderBox,n=t.border,f=t.margin,g=t.padding,a=(e=o,{top:(r=i).top+e.y,left:r.left+e.x,bottom:r.bottom+e.y,right:r.right+e.x});return d({borderBox:a,border:n,margin:f,padding:g})},a=function(t,o){var r={top:f(o.marginTop),right:f(o.marginRight),bottom:f(o.marginBottom),left:f(o.marginLeft)},e={top:f(o.paddingTop),right:f(o.paddingRight),bottom:f(o.paddingBottom),left:f(o.paddingLeft)},i={top:f(o.borderTopWidth),right:f(o.borderRightWidth),bottom:f(o.borderBottomWidth),left:f(o.borderLeftWidth)};return d({borderBox:t,margin:r,padding:e,border:i})};t.calculateBox=a,t.createBox=d,t.expand=e,t.getBox=function(t){var o=t.getBoundingClientRect(),r=window.getComputedStyle(t);return a(o,r)},t.getRect=r,t.offset=g,t.shrink=i,t.withScroll=function(t,o){return void 0===o&&(o={x:window.pageXOffset,y:window.pageYOffset}),g(t,o)},Object.defineProperty(t,"__esModule",{value:!0})});
