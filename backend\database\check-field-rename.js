const { Client } = require('pg');

async function checkFieldRename() {
  const client = new Client({
    host: '**************',
    port: 5435,
    user: 'user_jJSpPW',
    password: 'password_DmrhYX',
    database: 'inapp2',
  });

  try {
    await client.connect();
    console.log('✅ 连接到数据库成功');

    // 检查字段是否存在
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'app_home_configs'
      AND (column_name LIKE '%banner%' OR column_name LIKE '%float%')
      ORDER BY column_name;
    `);

    console.log('\n📋 app_home_configs 表中的相关字段:');
    if (result.rows.length > 0) {
      console.table(result.rows);
    } else {
      console.log('未找到相关字段');
    }

    // 检查是否还有旧字段
    const oldFieldCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'app_home_configs'
      AND column_name = 'top_float_ad_id';
    `);

    if (oldFieldCheck.rows.length > 0) {
      console.log('⚠️  旧字段 top_float_ad_id 仍然存在');
    } else {
      console.log('✅ 旧字段 top_float_ad_id 已成功删除');
    }

    // 检查新字段
    const newFieldCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'app_home_configs'
      AND column_name = 'top_banner_ad_id';
    `);

    if (newFieldCheck.rows.length > 0) {
      console.log('✅ 新字段 top_banner_ad_id 已成功创建');
    } else {
      console.log('❌ 新字段 top_banner_ad_id 不存在');
    }

    // 检查 applications 表字段
    console.log('\n📋 检查 applications 表字段:');
    const appResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'applications'
      AND column_name IN ('has_demo', 'has_mobile', 'has_desktop')
      ORDER BY column_name;
    `);

    if (appResult.rows.length > 0) {
      console.table(appResult.rows);
    } else {
      console.log('❌ applications 表缺少 has_demo, has_mobile, has_desktop 字段');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await client.end();
  }
}

checkFieldRename();
