{"name": "inapp2-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:local": "node start-local.js", "start:local:prod": "node start-local.js --prod", "start:supabase": "node ../start-environment.js supabase both", "start:local:full": "node ../start-environment.js local both", "start:backend:local": "node ../start-environment.js local backend", "start:backend:supabase": "node ../start-environment.js supabase backend", "db:setup": "node database/setup-local.js", "db:status": "node database/manage-database.js status --env local", "db:backup": "node database/manage-database.js backup --env local", "db:test": "node database/manage-database.js test --env local", "db:migrate": "node database/manage-database.js migrate --env local", "db:sync": "node database/manage-database.js sync", "db:sync:clear": "node database/manage-database.js sync --clear", "db:sync:fast": "node database/manage-database.js sync --no-backup --no-validate", "db:sync:complete": "node database/complete-supabase-sync.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/react-beautiful-dnd": "^13.1.8", "aws-sdk": "^2.1692.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "dotenv": "^16.5.0", "helmet": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "react-beautiful-dnd": "^13.1.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/aws-sdk": "^2.7.4", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.0", "@types/passport-local": "^1.0.38", "@types/pg": "^8.10.9", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}