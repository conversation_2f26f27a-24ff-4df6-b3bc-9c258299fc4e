{"name": "@types/react-beautiful-dnd", "version": "13.1.8", "description": "TypeScript definitions for react-beautiful-dnd", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-beautiful-dnd", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/varHarrie"}, {"name": "Bradley Ayers", "githubUsername": "bradleyayers", "url": "https://github.com/bradleyayers"}, {"name": "<PERSON>", "githubUsername": "paustint", "url": "https://github.com/paustint"}, {"name": "<PERSON>", "githubUsername": "marknel<PERSON><PERSON>", "url": "https://github.com/marknelissen"}, {"name": "<PERSON>", "githubUsername": "enricoboccadifuoco", "url": "https://github.com/enricoboccadifuoco"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "lony<PERSON>", "url": "https://github.com/lonyele"}, {"name": "Kanitkorn Sujautra", "githubUsername": "luk<PERSON><PERSON>", "url": "https://github.com/lukyth"}, {"name": "<PERSON><PERSON>", "githubUsername": "aruniverse", "url": "https://github.com/aruniverse"}, {"name": "<PERSON>", "githubUsername": "nick<PERSON><PERSON>", "url": "https://github.com/nickgarlis"}, {"name": "<PERSON>", "githubUsername": "brianspowers", "url": "https://github.com/brianspowers"}, {"name": "<PERSON>", "githubUsername": "declan-warn", "url": "https://github.com/declan-warn"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-beautiful-dnd"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "dd1101abbec6924c95af3a3d95170bf42a3b37d49ab44e19e508fd5603db4cd1", "typeScriptVersion": "4.6"}