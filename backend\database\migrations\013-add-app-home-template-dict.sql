-- =============================================
-- APP首页模板数据字典
-- =============================================

-- 添加APP首页模板配置数据字典
INSERT INTO sys_workbook (type, text, value, value_type, remark, "order") VALUES
-- APP首页模板类型
('app_home_template', 'BOX模式', 'box', 'text', 'BOX盒子模式，支持6宫格广告和游戏分类组', 1),
('app_home_template', '经典模式', 'classic', 'text', '经典首页模式，传统布局', 2),
('app_home_template', '卡片模式', 'card', 'text', '卡片式布局模式', 3),
('app_home_template', '瀑布流模式', 'waterfall', 'text', '瀑布流布局模式', 4),

-- APP首页广告位类型
('app_home_ad_position', '顶部Banner', 'top_banner', 'text', '页面顶部横幅广告位', 1),
('app_home_ad_position', '轮播广告', 'carousel', 'text', '首页轮播图广告位', 2),
('app_home_ad_position', '6宫格广告', 'grid_6', 'text', '首页6宫格广告位', 3),
('app_home_ad_position', '开屏弹窗', 'splash_popup', 'text', '应用启动时的开屏弹窗广告', 4),
('app_home_ad_position', '浮点广告', 'float_ad', 'text', '页面浮动广告位', 5),

-- APP首页浮点内容类型
('app_home_float_content', '公告通知', 'announcement', 'text', '系统公告和通知内容', 1),
('app_home_float_content', '活动推广', 'promotion', 'text', '活动推广内容', 2),
('app_home_float_content', '客服入口', 'customer_service', 'text', '客服联系入口', 3),
('app_home_float_content', '签到提醒', 'checkin_reminder', 'text', '每日签到提醒', 4),
('app_home_float_content', '充值优惠', 'recharge_bonus', 'text', '充值优惠信息', 5),

-- 游戏分类组显示模式
('game_category_display', '横向滚动', 'horizontal_scroll', 'text', '游戏分类横向滚动显示', 1),
('game_category_display', '网格布局', 'grid_layout', 'text', '游戏分类网格布局显示', 2),
('game_category_display', '列表模式', 'list_mode', 'text', '游戏分类列表模式显示', 3),
('game_category_display', '卡片模式', 'card_mode', 'text', '游戏分类卡片模式显示', 4),

-- BOX模式配置选项
('box_mode_config', '6宫格样式', 'grid_6_style', 'json', '{"rows": 2, "cols": 3, "spacing": 8, "borderRadius": 8}', 1),
('box_mode_config', '游戏分类最小数量', 'min_categories', 'text', '4', 2),
('box_mode_config', '每分类最小游戏数', 'min_games_per_category', 'text', '4', 3),
('box_mode_config', '推荐游戏最大数量', 'max_recommended_games', 'text', '6', 4),
('box_mode_config', '浮点内容最大数量', 'max_float_contents', 'text', '3', 5)

;

-- 添加注释说明
COMMENT ON TABLE sys_workbook IS '系统数据字典表，存储各种配置选项和枚举值';
