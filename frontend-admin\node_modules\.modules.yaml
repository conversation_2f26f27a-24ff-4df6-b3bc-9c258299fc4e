hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.2':
    '@adobe/css-tools': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@7.2.0':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.23.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/pro-card@2.9.6(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-card': private
  '@ant-design/pro-descriptions@2.6.6(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-descriptions': private
  '@ant-design/pro-field@3.0.3(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-field': private
  '@ant-design/pro-form@2.31.6(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-form': private
  '@ant-design/pro-layout@7.22.3(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-layout': private
  '@ant-design/pro-list@2.6.6(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-list': private
  '@ant-design/pro-provider@2.15.3(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-provider': private
  '@ant-design/pro-skeleton@2.2.1(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-skeleton': private
  '@ant-design/pro-table@3.18.6(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-table': private
  '@ant-design/pro-utils@2.16.4(antd@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/pro-utils': private
  '@ant-design/react-slick@1.1.2(react@18.3.1)':
    '@ant-design/react-slick': private
  '@antfu/install-pkg@1.0.0':
    '@antfu/install-pkg': private
  '@antfu/ni@24.3.0':
    '@antfu/ni': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': private
  '@babel/core@7.26.10':
    '@babel/core': private
  '@babel/generator@7.27.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.0':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.0':
    '@babel/helpers': private
  '@babel/parser@7.27.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@babel/template@7.27.0':
    '@babel/template': private
  '@babel/traverse@7.27.0':
    '@babel/traverse': private
  '@babel/types@7.27.0':
    '@babel/types': private
  '@chenshuai2144/sketch-color@1.0.9(react@18.3.1)':
    '@chenshuai2144/sketch-color': private
  '@clack/core@0.4.1':
    '@clack/core': private
  '@clack/prompts@0.10.0':
    '@clack/prompts': private
  '@commitlint/config-validator@19.8.0':
    '@commitlint/config-validator': private
  '@commitlint/ensure@19.8.0':
    '@commitlint/ensure': private
  '@commitlint/execute-rule@19.8.0':
    '@commitlint/execute-rule': private
  '@commitlint/format@19.8.0':
    '@commitlint/format': private
  '@commitlint/is-ignored@19.8.0':
    '@commitlint/is-ignored': private
  '@commitlint/lint@19.8.0':
    '@commitlint/lint': private
  '@commitlint/load@19.8.0(@types/node@22.13.13)(typescript@5.8.2)':
    '@commitlint/load': private
  '@commitlint/message@19.8.0':
    '@commitlint/message': private
  '@commitlint/parse@19.8.0':
    '@commitlint/parse': private
  '@commitlint/read@19.8.0':
    '@commitlint/read': private
  '@commitlint/resolve-extends@19.8.0':
    '@commitlint/resolve-extends': private
  '@commitlint/rules@19.8.0':
    '@commitlint/rules': private
  '@commitlint/to-lines@19.8.0':
    '@commitlint/to-lines': private
  '@commitlint/top-level@19.8.0':
    '@commitlint/top-level': private
  '@commitlint/types@19.8.0':
    '@commitlint/types': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@dnd-kit/accessibility@3.1.1(react@18.3.1)':
    '@dnd-kit/accessibility': private
  '@dnd-kit/modifiers@6.0.1(@dnd-kit/core@6.3.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    '@dnd-kit/modifiers': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/is-prop-valid@0.7.3':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.7.1':
    '@emotion/memoize': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@es-joy/jsdoccomment@0.50.0':
    '@es-joy/jsdoccomment': private
  '@esbuild/aix-ppc64@0.25.1':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.1':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.1':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.1':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.1':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.1':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.1':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.1':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.1':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.1':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.1':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.1':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.1':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.1':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.1':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.1':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.1':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.1':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.1':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.1':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.1':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.1':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.1':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.1':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.1':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-plugin-eslint-comments@4.4.1(eslint@9.23.0(jiti@2.4.2))':
    '@eslint-community/eslint-plugin-eslint-comments': private
  '@eslint-community/eslint-utils@4.5.1(eslint@9.23.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint-react/ast@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@eslint-react/ast': private
  '@eslint-react/core@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@eslint-react/core': private
  '@eslint-react/eff@1.38.0':
    '@eslint-react/eff': private
  '@eslint-react/jsx@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@eslint-react/jsx': private
  '@eslint-react/kit@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@eslint-react/kit': private
  '@eslint-react/shared@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@eslint-react/shared': private
  '@eslint-react/var@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@eslint-react/var': private
  '@eslint/compat@1.2.7(eslint@9.23.0(jiti@2.4.2))':
    '@eslint/compat': private
  '@eslint/config-array@0.19.2':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.12.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.23.0':
    '@eslint/js': private
  '@eslint/markdown@6.3.0':
    '@eslint/markdown': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.7':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@4.0.1':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@3.0.1':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.0':
    '@pkgr/core': private
  '@quansync/fs@0.1.1':
    '@quansync/fs': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/trigger': private
  '@rollup/pluginutils@5.1.4(rollup@4.37.0)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.37.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.37.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.37.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.37.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.37.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.37.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.37.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.37.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.37.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.37.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.37.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.37.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.37.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.37.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.37.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.37.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.37.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.37.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.37.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.37.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@stylistic/eslint-plugin@4.2.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@stylistic/eslint-plugin': private
  '@supabase/auth-js@2.70.0':
    '@supabase/auth-js': private
  '@supabase/functions-js@2.4.4':
    '@supabase/functions-js': private
  '@supabase/node-fetch@2.6.15':
    '@supabase/node-fetch': private
  '@supabase/postgrest-js@1.19.4':
    '@supabase/postgrest-js': private
  '@supabase/realtime-js@2.11.13(ws@8.18.1)':
    '@supabase/realtime-js': private
  '@supabase/storage-js@2.7.1':
    '@supabase/storage-js': private
  '@svgr/babel-plugin-add-jsx-attribute@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-add-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-remove-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  '@svgr/babel-plugin-svg-dynamic-title@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-svg-dynamic-title': private
  '@svgr/babel-plugin-svg-em-dimensions@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-svg-em-dimensions': private
  '@svgr/babel-plugin-transform-react-native-svg@8.1.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-transform-react-native-svg': private
  '@svgr/babel-plugin-transform-svg-component@8.0.0(@babel/core@7.26.10)':
    '@svgr/babel-plugin-transform-svg-component': private
  '@svgr/babel-preset@8.1.0(@babel/core@7.26.10)':
    '@svgr/babel-preset': private
  '@svgr/core@8.1.0(typescript@5.8.2)':
    '@svgr/core': private
  '@svgr/hast-util-to-babel-ast@8.0.0':
    '@svgr/hast-util-to-babel-ast': private
  '@tanstack/query-core@5.69.0':
    '@tanstack/query-core': private
  '@tanstack/query-devtools@5.67.2':
    '@tanstack/query-devtools': private
  '@testing-library/dom@10.4.0':
    '@testing-library/dom': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.6.8':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.6':
    '@types/babel__traverse': private
  '@types/conventional-commits-parser@5.0.1':
    '@types/conventional-commits-parser': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/doctrine@0.0.9':
    '@types/doctrine': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/hoist-non-react-statics@3.3.6':
    '@types/hoist-non-react-statics': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/phoenix@1.6.6':
    '@types/phoenix': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/react-redux@7.1.34':
    '@types/react-redux': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@typescript-eslint/eslint-plugin@8.28.0(@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2))(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.28.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.28.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.28.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.28.0(typescript@5.8.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.28.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.28.0':
    '@typescript-eslint/visitor-keys': private
  '@umijs/route-utils@4.0.1':
    '@umijs/route-utils': private
  '@umijs/use-params@1.0.9(react@18.3.1)':
    '@umijs/use-params': private
  '@unrs/rspack-resolver-binding-darwin-arm64@1.3.0':
    '@unrs/rspack-resolver-binding-darwin-arm64': private
  '@unrs/rspack-resolver-binding-darwin-x64@1.3.0':
    '@unrs/rspack-resolver-binding-darwin-x64': private
  '@unrs/rspack-resolver-binding-freebsd-x64@1.3.0':
    '@unrs/rspack-resolver-binding-freebsd-x64': private
  '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm-gnueabihf': private
  '@unrs/rspack-resolver-binding-linux-arm-musleabihf@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm-musleabihf': private
  '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm64-gnu': private
  '@unrs/rspack-resolver-binding-linux-arm64-musl@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm64-musl': private
  '@unrs/rspack-resolver-binding-linux-ppc64-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-ppc64-gnu': private
  '@unrs/rspack-resolver-binding-linux-s390x-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-s390x-gnu': private
  '@unrs/rspack-resolver-binding-linux-x64-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-x64-gnu': private
  '@unrs/rspack-resolver-binding-linux-x64-musl@1.3.0':
    '@unrs/rspack-resolver-binding-linux-x64-musl': private
  '@unrs/rspack-resolver-binding-wasm32-wasi@1.3.0':
    '@unrs/rspack-resolver-binding-wasm32-wasi': private
  '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.3.0':
    '@unrs/rspack-resolver-binding-win32-arm64-msvc': private
  '@unrs/rspack-resolver-binding-win32-ia32-msvc@1.3.0':
    '@unrs/rspack-resolver-binding-win32-ia32-msvc': private
  '@unrs/rspack-resolver-binding-win32-x64-msvc@1.3.0':
    '@unrs/rspack-resolver-binding-win32-x64-msvc': private
  '@vitest/eslint-plugin@1.1.38(@typescript-eslint/utils@8.28.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2))(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2)(vitest@3.0.9(@types/debug@4.1.12)(@types/node@22.13.13)(happy-dom@17.4.4)(jiti@2.4.2)(yaml@2.7.0))':
    '@vitest/eslint-plugin': private
  '@vitest/expect@3.0.9':
    '@vitest/expect': private
  '@vitest/mocker@3.0.9(vite@6.2.3(@types/node@22.13.13)(jiti@2.4.2)(yaml@2.7.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.0.9':
    '@vitest/pretty-format': private
  '@vitest/runner@3.0.9':
    '@vitest/runner': private
  '@vitest/snapshot@3.0.9':
    '@vitest/snapshot': private
  '@vitest/spy@3.0.9':
    '@vitest/spy': private
  '@vitest/utils@3.0.9':
    '@vitest/utils': private
  '@vue/compiler-core@3.5.13':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.13':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.13':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.13':
    '@vue/compiler-ssr': private
  '@vue/shared@3.5.13':
    '@vue/shared': private
  JSONStream@1.3.5:
    JSONStream: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  add-dom-event-listener@1.1.0:
    add-dom-event-listener: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@3.17.0:
    ansis: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-ify@1.0.0:
    array-ify: private
  assertion-error@2.0.1:
    assertion-error: private
  async@3.2.6:
    async: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  birecord@0.1.1:
    birecord: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  builtin-modules@4.0.0:
    builtin-modules: private
  bundle-import@0.0.2:
    bundle-import: private
  bundle-name@4.1.0:
    bundle-name: private
  bundle-require@5.1.0(esbuild@0.25.1):
    bundle-require: private
  cac@6.7.14:
    cac: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001707:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chai@5.2.0:
    chai: private
  chalk@3.0.0:
    chalk: private
  character-entities@2.0.2:
    character-entities: private
  check-error@2.1.1:
    check-error: private
  chokidar@4.0.3:
    chokidar: private
  ci-info@4.2.0:
    ci-info: private
  classnames@2.5.1:
    classnames: private
  clean-regexp@1.0.0:
    clean-regexp: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-truncate@4.0.0:
    cli-truncate: private
  cliui@8.0.1:
    cliui: private
  code-inspector-core@0.20.6:
    code-inspector-core: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  commander@13.1.0:
    commander: private
  comment-parser@1.4.1:
    comment-parser: private
  compare-func@2.0.0:
    compare-func: private
  compare-versions@6.1.1:
    compare-versions: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  conventional-changelog-angular@7.0.0:
    conventional-changelog-angular: private
  conventional-changelog-conventionalcommits@7.0.2:
    conventional-changelog-conventionalcommits: private
  conventional-commits-parser@5.0.0:
    conventional-commits-parser: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-es@1.2.2:
    cookie-es: private
  cookie@1.0.2:
    cookie: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  core-js-compat@3.41.0:
    core-js-compat: private
  cosmiconfig-typescript-loader@6.1.0(@types/node@22.13.13)(cosmiconfig@9.0.0(typescript@5.8.2))(typescript@5.8.2):
    cosmiconfig-typescript-loader: private
  cosmiconfig@8.3.6(typescript@5.8.2):
    cosmiconfig: private
  countup.js@2.8.0:
    countup.js: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crossws@0.3.4:
    crossws: private
  css-box-model@1.2.1:
    css-box-model: private
  css-jss@10.10.0:
    css-jss: private
  css-select@5.1.0:
    css-select: private
  css-tree@2.3.1:
    css-tree: private
  css-vendor@2.0.8:
    css-vendor: private
  css-what@6.1.0:
    css-what: private
  css.escape@1.5.1:
    css.escape: private
  cssesc@3.0.0:
    cssesc: private
  csso@5.0.5:
    csso: private
  csstype@3.1.3:
    csstype: private
  dargs@8.1.0:
    dargs: private
  debug@4.4.0:
    debug: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  defu@6.1.4:
    defu: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.3:
    destr: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  docs:
    docs: private
  doctrine@3.0.0:
    doctrine: private
  dom-accessibility-api@0.6.3:
    dom-accessibility-api: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dot-prop@5.3.0:
    dot-prop: private
  dotenv@16.4.7:
    dotenv: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.123:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  environment@1.1.0:
    environment: private
  error-ex@1.3.2:
    error-ex: private
  es-module-lexer@1.6.0:
    es-module-lexer: private
  esbuild-code-inspector-plugin@0.20.6:
    esbuild-code-inspector-plugin: private
  esbuild@0.25.1:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-compat-utils@0.6.4(eslint@9.23.0(jiti@2.4.2)):
    eslint-compat-utils: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.0.1:
    eslint-flat-config-utils: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-json-compat-utils@0.2.1(eslint@9.23.0(jiti@2.4.2))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: private
  eslint-merge-processors@2.0.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-merge-processors: private
  eslint-plugin-antfu@3.1.1(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-antfu: private
  eslint-plugin-command@3.2.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-es-x: private
  eslint-plugin-import-x@4.9.2(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-import-x: private
  eslint-plugin-jsdoc@50.6.9(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.20.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.17.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-n: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-perfectionist@4.10.1(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-perfectionist: private
  eslint-plugin-pnpm@0.3.1(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-pnpm: private
  eslint-plugin-react-debug@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-react-debug: private
  eslint-plugin-react-dom@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-react-dom: private
  eslint-plugin-react-hooks-extra@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-react-hooks-extra: private
  eslint-plugin-react-naming-convention@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-react-naming-convention: private
  eslint-plugin-react-web-api@1.38.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    eslint-plugin-react-web-api: private
  eslint-plugin-react-x@1.38.0(eslint@9.23.0(jiti@2.4.2))(ts-api-utils@2.1.0(typescript@5.8.2))(typescript@5.8.2):
    eslint-plugin-react-x: private
  eslint-plugin-regexp@2.7.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-regexp: private
  eslint-plugin-toml@0.12.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-toml: private
  eslint-plugin-unicorn@57.0.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.28.0(@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2))(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2))(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vue@10.0.0(eslint@9.23.0(jiti@2.4.2))(vue-eslint-parser@10.1.1(eslint@9.23.0(jiti@2.4.2))):
    eslint-plugin-vue: private
  eslint-plugin-yml@1.17.0(eslint@9.23.0(jiti@2.4.2)):
    eslint-plugin-yml: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.13)(eslint@9.23.0(jiti@2.4.2)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@5.0.1:
    eventemitter3: private
  execa@8.0.1:
    execa: private
  expect-type@1.2.0:
    expect-type: private
  exsolve@1.0.4:
    exsolve: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@7.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  framer-motion@12.6.0(@emotion/is-prop-valid@0.7.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    framer-motion: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  fzf@0.5.2:
    fzf: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-port-please@3.1.2:
    get-port-please: private
  get-stream@8.0.1:
    get-stream: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  git-raw-commits@4.0.0:
    git-raw-commits: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  h3@1.15.1:
    h3: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  html-parse-stringify@3.0.1:
    html-parse-stringify: private
  human-signals@5.0.0:
    human-signals: private
  hyphenate-style-name@1.1.0:
    hyphenate-style-name: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-from-esm@1.3.4:
    import-from-esm: private
  import-from-string@0.0.5:
    import-from-string: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  index-to-position@1.0.0:
    index-to-position: private
  ini@4.1.1:
    ini: private
  intersection-observer@0.12.2:
    intersection-observer: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-builtin-module@4.0.0:
    is-builtin-module: private
  is-core-module@2.16.1:
    is-core-module: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-immutable-type@5.0.1(eslint@9.23.0(jiti@2.4.2))(typescript@5.8.2):
    is-immutable-type: private
  is-in-browser@1.1.3:
    is-in-browser: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-number@7.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-stream@3.0.0:
    is-stream: private
  is-text-path@2.0.0:
    is-text-path: private
  is-wsl@3.1.0:
    is-wsl: private
  isexe@2.0.0:
    isexe: private
  isows@1.0.7(ws@8.18.1):
    isows: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@2.4.2:
    jiti: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  jsonparse@1.3.1:
    jsonparse: private
  jss-plugin-camel-case@10.10.0:
    jss-plugin-camel-case: private
  jss-plugin-compose@10.10.0:
    jss-plugin-compose: private
  jss-plugin-default-unit@10.10.0:
    jss-plugin-default-unit: private
  jss-plugin-expand@10.10.0:
    jss-plugin-expand: private
  jss-plugin-extend@10.10.0:
    jss-plugin-extend: private
  jss-plugin-global@10.10.0:
    jss-plugin-global: private
  jss-plugin-nested@10.10.0:
    jss-plugin-nested: private
  jss-plugin-props-sort@10.10.0:
    jss-plugin-props-sort: private
  jss-plugin-rule-value-function@10.10.0:
    jss-plugin-rule-value-function: private
  jss-plugin-rule-value-observable@10.10.0:
    jss-plugin-rule-value-observable: private
  jss-plugin-template@10.10.0:
    jss-plugin-template: private
  jss-plugin-vendor-prefixer@10.10.0:
    jss-plugin-vendor-prefixer: private
  jss-preset-default@10.10.0:
    jss-preset-default: private
  jss@10.10.0:
    jss: private
  keyv@4.5.4:
    keyv: private
  launch-ide@1.0.5:
    launch-ide: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listr2@8.2.5:
    listr2: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash.snakecase@4.1.1:
    lodash.snakecase: private
  lodash.startcase@4.4.0:
    lodash.startcase: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: private
  lodash@4.17.21:
    lodash: private
  log-update@6.1.0:
    log-update: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@3.1.3:
    loupe: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@5.1.1:
    lru-cache: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  markdown-table@3.0.4:
    markdown-table: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.0.30:
    mdn-data: private
  memoize-one@5.2.1:
    memoize-one: private
  meow@12.1.1:
    meow: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mlly@1.7.4:
    mlly: private
  motion-dom@12.6.0:
    motion-dom: private
  motion-utils@12.5.0:
    motion-utils: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  no-case@3.0.4:
    no-case: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-mock-http@1.0.0:
    node-mock-http: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@6.0.2:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-wheel@1.0.1:
    normalize-wheel: private
  npm-run-path@6.0.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  ofetch@1.4.1:
    ofetch: private
  onetime@6.0.0:
    onetime: private
  open@10.1.0:
    open: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@1.1.0:
    package-manager-detector: private
  parent-module@1.0.1:
    parent-module: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports@2.2.1:
    parse-imports: private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@2.3.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  pkg-types@2.1.0:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  pnpm-workspace-yaml@0.1.2:
    pnpm-workspace-yaml: private
  portfinder@1.0.35:
    portfinder: private
  postcss-import@15.1.0(postcss@8.5.3):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.3):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.3):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.3):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@27.5.1:
    pretty-format: private
  prop-types@15.8.1:
    prop-types: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  radix3@1.1.2:
    radix3: private
  raf-schd@4.0.3:
    raf-schd: private
  rc-cascader@3.33.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dialog: private
  rc-drawer@7.2.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-field-form: private
  rc-image@7.11.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-image: private
  rc-input-number@9.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input-number: private
  rc-input@1.7.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input: private
  rc-mentions@2.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-menu: private
  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-motion: private
  rc-notification@5.6.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-picker: private
  rc-progress@4.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-progress: private
  rc-rate@2.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-segmented: private
  rc-select@14.16.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-select: private
  rc-slider@11.1.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-slider: private
  rc-steps@6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-steps: private
  rc-switch@4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-switch: private
  rc-table@7.50.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-table: private
  rc-tabs@15.5.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tabs: private
  rc-textarea@1.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree: private
  rc-upload@4.8.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-upload: private
  rc-util@5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-util: private
  rc-virtual-list@3.18.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-virtual-list: private
  react-display-name@0.2.5:
    react-display-name: private
  react-easy-crop@5.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-easy-crop: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-is@16.13.1:
    react-is: private
  react-lifecycles-compat@3.0.4:
    react-lifecycles-compat: private
  react-redux@7.2.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-redux: private
  react-refresh@0.14.2:
    react-refresh: private
  reactcss@1.2.3(react@18.3.1):
    reactcss: private
  read-cache@1.0.0:
    read-cache: private
  read-package-up@11.0.0:
    read-package-up: private
  read-pkg@9.0.1:
    read-pkg: private
  readdirp@3.6.0:
    readdirp: private
  redent@3.0.0:
    redent: private
  redux@4.2.1:
    redux: private
  refa@0.12.1:
    refa: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regjsparser@0.12.0:
    regjsparser: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup-plugin-visualizer@5.14.0(rollup@4.37.0):
    rollup-plugin-visualizer: private
  rollup@4.37.0:
    rollup: private
  rspack-resolver@1.3.0:
    rspack-resolver: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  scheduler@0.23.2:
    scheduler: private
  screenfull@5.2.0:
    screenfull: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  scslre@0.3.0:
    scslre: private
  semver@6.3.1:
    semver: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  shallow-equal@1.2.1:
    shallow-equal: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  simplebar-core@1.3.0:
    simplebar-core: private
  sisteransi@1.0.5:
    sisteransi: private
  size-sensor@1.0.2:
    size-sensor: private
  slashes@3.0.12:
    slashes: private
  slice-ansi@5.0.0:
    slice-ansi: private
  snake-case@3.0.4:
    snake-case: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.7.4:
    source-map: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  split2@4.2.0:
    split2: private
  stable-hash@0.0.5:
    stable-hash: private
  stackback@0.0.2:
    stackback: private
  std-env@3.8.1:
    std-env: private
  string-argv@0.3.2:
    string-argv: private
  string-convert@0.2.1:
    string-convert: private
  string-ts@2.2.1:
    string-ts: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stylis@4.3.6:
    stylis: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-parser@2.0.4:
    svg-parser: private
  svgo@3.3.2:
    svgo: private
  swr@2.3.3(react@18.3.1):
    swr: private
  symbol-observable@1.2.0:
    symbol-observable: private
  synckit@0.9.2:
    synckit: private
  tapable@2.2.1:
    tapable: private
  text-extensions@2.4.0:
    text-extensions: private
  theming@3.3.0(react@18.3.1):
    theming: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  through@2.3.8:
    through: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinybench@2.9.0:
    tinybench: private
  tinycolor2@1.6.0:
    tinycolor2: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.12:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@2.1.0(typescript@5.8.2):
    ts-api-utils: private
  ts-declaration-location@1.0.7(typescript@5.8.2):
    ts-declaration-location: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-pattern@5.6.2:
    ts-pattern: private
  tslib@2.8.1:
    tslib: private
  turbo-stream@2.4.0:
    turbo-stream: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.38.0:
    type-fest: private
  ufo@1.5.4:
    ufo: private
  unconfig@7.3.1:
    unconfig: private
  uncrypto@0.1.3:
    uncrypto: private
  undici-types@6.20.0:
    undici-types: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-memo-one@1.1.3(react@18.3.1):
    use-memo-one: private
  use-sync-external-store@1.4.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vite-code-inspector-plugin@0.20.6:
    vite-code-inspector-plugin: private
  vite-node@3.0.9(@types/node@22.13.13)(jiti@2.4.2)(yaml@2.7.0):
    vite-node: private
  void-elements@3.1.0:
    void-elements: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-eslint-parser@10.1.1(eslint@9.23.0(jiti@2.4.2)):
    vue-eslint-parser: private
  warning@4.0.3:
    warning: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-code-inspector-plugin@0.20.6:
    webpack-code-inspector-plugin: private
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  ws@8.18.1:
    ws: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.7.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zrender@5.6.1:
    zrender: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.4
pendingBuilds: []
prunedAt: Thu, 26 Jun 2025 13:22:20 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@emnapi/core@1.3.1'
  - '@emnapi/runtime@1.3.1'
  - '@emnapi/wasi-threads@1.0.1'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.24.2'
  - '@esbuild/aix-ppc64@0.25.1'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.24.2'
  - '@esbuild/android-arm64@0.25.1'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.24.2'
  - '@esbuild/android-arm@0.25.1'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.24.2'
  - '@esbuild/android-x64@0.25.1'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.24.2'
  - '@esbuild/darwin-arm64@0.25.1'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.24.2'
  - '@esbuild/darwin-x64@0.25.1'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.24.2'
  - '@esbuild/freebsd-arm64@0.25.1'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.24.2'
  - '@esbuild/freebsd-x64@0.25.1'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.24.2'
  - '@esbuild/linux-arm64@0.25.1'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.24.2'
  - '@esbuild/linux-arm@0.25.1'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.24.2'
  - '@esbuild/linux-ia32@0.25.1'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.24.2'
  - '@esbuild/linux-loong64@0.25.1'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.24.2'
  - '@esbuild/linux-mips64el@0.25.1'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.24.2'
  - '@esbuild/linux-ppc64@0.25.1'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.24.2'
  - '@esbuild/linux-riscv64@0.25.1'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.24.2'
  - '@esbuild/linux-s390x@0.25.1'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.24.2'
  - '@esbuild/linux-x64@0.25.1'
  - '@esbuild/netbsd-arm64@0.24.2'
  - '@esbuild/netbsd-arm64@0.25.1'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.24.2'
  - '@esbuild/netbsd-x64@0.25.1'
  - '@esbuild/openbsd-arm64@0.24.2'
  - '@esbuild/openbsd-arm64@0.25.1'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.24.2'
  - '@esbuild/openbsd-x64@0.25.1'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.24.2'
  - '@esbuild/sunos-x64@0.25.1'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.24.2'
  - '@esbuild/win32-arm64@0.25.1'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.24.2'
  - '@esbuild/win32-ia32@0.25.1'
  - '@napi-rs/wasm-runtime@0.2.7'
  - '@rollup/rollup-android-arm-eabi@4.37.0'
  - '@rollup/rollup-android-arm64@4.37.0'
  - '@rollup/rollup-darwin-arm64@4.37.0'
  - '@rollup/rollup-darwin-x64@4.37.0'
  - '@rollup/rollup-freebsd-arm64@4.37.0'
  - '@rollup/rollup-freebsd-x64@4.37.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.37.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.37.0'
  - '@rollup/rollup-linux-arm64-gnu@4.37.0'
  - '@rollup/rollup-linux-arm64-musl@4.37.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.37.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.37.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.37.0'
  - '@rollup/rollup-linux-riscv64-musl@4.37.0'
  - '@rollup/rollup-linux-s390x-gnu@4.37.0'
  - '@rollup/rollup-linux-x64-gnu@4.37.0'
  - '@rollup/rollup-linux-x64-musl@4.37.0'
  - '@rollup/rollup-win32-arm64-msvc@4.37.0'
  - '@rollup/rollup-win32-ia32-msvc@4.37.0'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/rspack-resolver-binding-darwin-arm64@1.3.0'
  - '@unrs/rspack-resolver-binding-darwin-x64@1.3.0'
  - '@unrs/rspack-resolver-binding-freebsd-x64@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm-musleabihf@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm64-musl@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-ppc64-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-s390x-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-x64-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-x64-musl@1.3.0'
  - '@unrs/rspack-resolver-binding-wasm32-wasi@1.3.0'
  - '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.3.0'
  - '@unrs/rspack-resolver-binding-win32-ia32-msvc@1.3.0'
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\inwork\inapp\frontend-admin\node_modules\.pnpm
virtualStoreDirMaxLength: 60
