import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  Card,
  Typography,
  Divider,
  Space,
  Tag,
  Tabs,
  Alert,
  But<PERSON>,
  Tooltip
} from 'antd';
import {
  AppstoreOutlined,
  PictureOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import GameCategoryConfig from './GameCategoryConfig';
import FloatContentConfig from './FloatContentConfig';
import type { Application } from '#src/api/application';
import type { AdConfig } from '#src/api/config';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 表单数据接口
interface BoxModeFormData {
  configName: string;
  description?: string;
  templateType: string;
  status: number;
  sortOrder?: number;
  remark?: string;
  
  // 广告配置
  topBannerAdId?: number;
  carouselAdId?: number;
  homeGridAdId: number; // BOX模式必需6宫格广告
  splashPopupAdId?: number;
  floatAdId?: number;
  
  // BOX模式特有配置
  floatContents: any[];
  gameCategories: any[];
}

// 组件属性接口
interface BoxModeFormProps {
  form: any;
  adOptions: AdConfig[];
  applications: Application[];
  initialValues?: Partial<BoxModeFormData>;
  disabled?: boolean;
}

// 模板样式选项 - 不同的排版风格和视觉呈现
const TEMPLATE_OPTIONS = [
  { value: 'box', label: 'BOX风格', description: '盒子布局风格，支持4宫格、浮点内容、游戏分类组等' },
  { value: 'classic', label: '经典风格', description: '传统列表布局风格，简洁明了' },
  { value: 'card', label: '卡片风格', description: '卡片式布局，现代化设计风格' },
  { value: 'grid', label: '网格风格', description: '网格布局，整齐排列' }
];

// 状态选项
const STATUS_OPTIONS = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
];

const BoxModeForm: React.FC<BoxModeFormProps> = ({
  form,
  adOptions = [],
  applications = [],
  initialValues,
  disabled = false
}) => {
  const [templateType, setTemplateType] = useState<string>('box');
  const [activeTab, setActiveTab] = useState<string>('basic');

  // 广告类型过滤函数
  const getAdsByType = (adType: number) => {
    return adOptions.filter(ad => ad.adType === adType);
  };

  // 各类型广告选项
  const carouselAdOptions = getAdsByType(1);  // 轮播广告
  const popupAdOptions = getAdsByType(2);     // 弹窗广告
  const floatAdOptions = getAdsByType(3);     // 浮点广告
  const bannerAdOptions = getAdsByType(5);    // Banner广告
  const gridAdOptions = getAdsByType(7);      // 4宫格广告

  // 监听模板类型变化
  const handleTemplateTypeChange = (value: string) => {
    setTemplateType(value);
    form.setFieldValue('templateType', value);
  };

  // 获取验证状态
  const getValidationStatus = () => {
    const values = form.getFieldsValue();
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本信息验证
    if (!values.configName) {
      errors.push('配置名称不能为空');
    }

    if (templateType === 'box') {
      // BOX模式特殊验证
      if (!values.homeGridAdId) {
        errors.push('BOX模式必须选择6宫格广告');
      }

      // 游戏分类组验证
      const categories = values.gameCategories || [];
      if (categories.length < 4) {
        errors.push('BOX模式至少需要4个游戏分类组');
      }

      categories.forEach((cat: any, index: number) => {
        if (!cat.categoryTitle?.zh || !cat.categoryTitle?.en) {
          errors.push(`第${index + 1}个分类组标题不完整`);
        }
        if (!cat.games || cat.games.length < 4) {
          errors.push(`第${index + 1}个分类组至少需要4个游戏`);
        }
      });

      // 浮点内容验证
      const floatContents = values.floatContents || [];
      if (floatContents.length > 3) {
        errors.push('浮点内容最多只能配置3个');
      }

      // 推荐配置提醒
      if (!values.topBannerAdId) {
        warnings.push('建议配置顶部Banner广告以提升用户体验');
      }
      if (!values.carouselAdId) {
        warnings.push('建议配置轮播广告以展示重要内容');
      }
      if (floatContents.length === 0) {
        warnings.push('建议配置浮点内容以增加互动性');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  };

  const validation = getValidationStatus();

  return (
    <div>
      {/* 验证状态提示 */}
      {!validation.isValid && (
        <Alert
          message="配置验证失败"
          description={
            <ul style={{ margin: '8px 0 0 20px' }}>
              {validation.errors.map((error, index) => (
                <li key={index} style={{ color: '#ff4d4f' }}>{error}</li>
              ))}
            </ul>
          }
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {validation.warnings.length > 0 && (
        <Alert
          message="配置建议"
          description={
            <ul style={{ margin: '8px 0 0 20px' }}>
              {validation.warnings.map((warning, index) => (
                <li key={index} style={{ color: '#faad14' }}>{warning}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
        {/* 基本配置 */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              基本配置
              {!validation.isValid && <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginLeft: 4 }} />}
            </span>
          }
          key="basic"
        >
          <Card size="small" title="基本信息">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="configName"
                  label="配置名称"
                  rules={[{ required: true, message: '请输入配置名称' }]}
                >
                  <Input placeholder="请输入配置名称" disabled={disabled} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="templateType"
                  label="模板样式"
                  rules={[{ required: true, message: '请选择模板样式' }]}
                >
                  <Select
                    placeholder="请选择模板样式"
                    onChange={handleTemplateTypeChange}
                    disabled={disabled}
                  >
                    {TEMPLATE_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        <div>
                          <div>{option.label}</div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {option.description}
                          </Text>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select placeholder="请选择状态" disabled={disabled}>
                    {STATUS_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="sortOrder"
                  label="排序"
                >
                  <InputNumber 
                    placeholder="请输入排序值" 
                    min={0} 
                    max={9999}
                    style={{ width: '100%' }}
                    disabled={disabled}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="配置描述"
            >
              <TextArea 
                placeholder="请输入配置描述" 
                rows={3}
                maxLength={500}
                showCount
                disabled={disabled}
              />
            </Form.Item>

            <Form.Item
              name="remark"
              label="备注"
            >
              <TextArea 
                placeholder="请输入备注" 
                rows={2}
                maxLength={500}
                showCount
                disabled={disabled}
              />
            </Form.Item>
          </Card>
        </TabPane>

        {/* 广告配置 */}
        <TabPane
          tab={
            <span>
              <PictureOutlined />
              广告配置
            </span>
          }
          key="ads"
        >
          <Card size="small" title="广告位配置">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="topBannerAdId"
                  label="顶部Banner广告"
                  extra="显示在页面顶部的Banner广告"
                >
                  <Select placeholder="请选择Banner广告" allowClear showSearch disabled={disabled}>
                    {bannerAdOptions.length > 0 ? bannerAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无Banner广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="carouselAdId"
                  label="轮播广告"
                  extra="首页顶部轮播展示的广告"
                >
                  <Select placeholder="请选择轮播广告" allowClear showSearch disabled={disabled}>
                    {carouselAdOptions.length > 0 ? carouselAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无轮播广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {templateType === 'box' && (
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="homeGridAdId"
                    label={
                      <span>
                        4宫格广告
                        <Tag color="red" style={{ marginLeft: 8 }}>BOX模式必需</Tag>
                      </span>
                    }
                    rules={[{ required: true, message: 'BOX模式必须选择4宫格广告' }]}
                    extra="BOX模式的核心广告位，展示4个重要功能入口"
                  >
                    <Select placeholder="请选择4宫格广告" showSearch disabled={disabled}>
                      {gridAdOptions.length > 0 ? gridAdOptions.map((ad: AdConfig) => (
                        <Option key={ad.id} value={ad.id}>
                          {ad.title} ({ad.adIdentifier})
                        </Option>
                      )) : (
                        <Option value="" disabled>
                          暂无4宫格广告，请先创建
                        </Option>
                      )}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            )}

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="splashPopupAdId"
                  label="开屏弹窗广告"
                  extra="APP启动时显示的弹窗广告"
                >
                  <Select placeholder="请选择弹窗广告" allowClear showSearch disabled={disabled}>
                    {popupAdOptions.length > 0 ? popupAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无弹窗广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="floatAdId"
                  label="浮点广告"
                  extra="页面上的浮动广告位"
                >
                  <Select placeholder="请选择浮点广告" allowClear showSearch disabled={disabled}>
                    {floatAdOptions.length > 0 ? floatAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无浮点广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </TabPane>

        {/* BOX模式特有配置 */}
        {templateType === 'box' && (
          <>
            {/* 浮点内容配置 */}
            <TabPane
              tab={
                <span>
                  <InfoCircleOutlined />
                  浮点内容
                  <Tag color="blue" style={{ marginLeft: 4 }}>BOX</Tag>
                </span>
              }
              key="float"
            >
              <Card size="small" title="浮点内容配置" extra={
                <Tooltip title="浮点内容将在APP首页以浮层形式显示，最多支持3个">
                  <InfoCircleOutlined />
                </Tooltip>
              }>
                <Form.Item name="floatContents" noStyle>
                  <FloatContentConfig
                    disabled={disabled}
                    maxCount={3}
                  />
                </Form.Item>
              </Card>
            </TabPane>

            {/* 游戏分类组配置 */}
            <TabPane
              tab={
                <span>
                  <AppstoreOutlined />
                  游戏分类
                  <Tag color="green" style={{ marginLeft: 4 }}>BOX</Tag>
                </span>
              }
              key="categories"
            >
              <Card size="small" title="游戏分类组配置" extra={
                <Tooltip title="BOX模式至少需要4个游戏分类组，每个分类组至少4个游戏">
                  <InfoCircleOutlined />
                </Tooltip>
              }>
                <Form.Item name="gameCategories" noStyle>
                  <GameCategoryConfig
                    applications={applications}
                    disabled={disabled}
                  />
                </Form.Item>
              </Card>
            </TabPane>
          </>
        )}

        {/* 配置预览 */}
        <TabPane
          tab={
            <span>
              <CheckCircleOutlined />
              配置预览
            </span>
          }
          key="preview"
        >
          <Card size="small" title="配置总览">
            <ConfigPreview
              form={form}
              templateType={templateType}
              validation={validation}
              adOptions={adOptions}
              applications={applications}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

// 配置预览组件
const ConfigPreview: React.FC<{
  form: any;
  templateType: string;
  validation: any;
  adOptions: AdConfig[];
  applications: Application[];
}> = ({ form, templateType, validation, adOptions, applications }) => {
  const values = form.getFieldsValue();

  const getAdName = (adId: number) => {
    const ad = adOptions.find(a => a.id === adId);
    return ad ? `${ad.title} (${ad.adIdentifier})` : '未选择';
  };

  const getGameName = (gameId: number) => {
    const game = applications.find(a => a.id === gameId);
    return game ? game.name : '未知游戏';
  };

  return (
    <div>
      {/* 验证状态 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={5}>
          配置状态
          {validation.isValid ? (
            <Tag color="green" style={{ marginLeft: 8 }}>
              <CheckCircleOutlined /> 验证通过
            </Tag>
          ) : (
            <Tag color="red" style={{ marginLeft: 8 }}>
              <ExclamationCircleOutlined /> 验证失败
            </Tag>
          )}
        </Title>

        {!validation.isValid && (
          <Alert
            message="配置问题"
            description={validation.errors.join('; ')}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {validation.warnings.length > 0 && (
          <Alert
            message="配置建议"
            description={validation.warnings.join('; ')}
            type="warning"
            showIcon
          />
        )}
      </div>

      {/* 基本信息预览 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={5}>基本信息</Title>
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>配置名称：</Text>
            <div>{values.configName || '-'}</div>
          </Col>
          <Col span={8}>
            <Text strong>模板样式：</Text>
            <div>
              <Tag color={templateType === 'box' ? 'blue' : 'default'}>
                {TEMPLATE_OPTIONS.find(t => t.value === templateType)?.label}
              </Tag>
            </div>
          </Col>
          <Col span={8}>
            <Text strong>状态：</Text>
            <div>
              <Tag color={values.status === 1 ? 'green' : 'red'}>
                {values.status === 1 ? '启用' : '禁用'}
              </Tag>
            </div>
          </Col>
        </Row>
      </div>

      {/* 广告配置预览 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={5}>广告配置</Title>
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>顶部Banner广告：</Text>
            <div>{values.topBannerAdId ? getAdName(values.topBannerAdId) : '未配置'}</div>
          </Col>
          <Col span={12}>
            <Text strong>轮播广告：</Text>
            <div>{values.carouselAdId ? getAdName(values.carouselAdId) : '未配置'}</div>
          </Col>
        </Row>

        {templateType === 'box' && (
          <Row gutter={16} style={{ marginTop: 12 }}>
            <Col span={24}>
              <Text strong>6宫格广告：</Text>
              <div>
                {values.homeGridAdId ? (
                  <Tag color="blue">{getAdName(values.homeGridAdId)}</Tag>
                ) : (
                  <Tag color="red">未配置（必需）</Tag>
                )}
              </div>
            </Col>
          </Row>
        )}

        <Row gutter={16} style={{ marginTop: 12 }}>
          <Col span={12}>
            <Text strong>开屏弹窗广告：</Text>
            <div>{values.splashPopupAdId ? getAdName(values.splashPopupAdId) : '未配置'}</div>
          </Col>
          <Col span={12}>
            <Text strong>浮点广告：</Text>
            <div>{values.floatAdId ? getAdName(values.floatAdId) : '未配置'}</div>
          </Col>
        </Row>
      </div>

      {/* BOX模式特有配置预览 */}
      {templateType === 'box' && (
        <>
          {/* 浮点内容预览 */}
          <div style={{ marginBottom: 24 }}>
            <Title level={5}>浮点内容配置</Title>
            {values.floatContents && values.floatContents.length > 0 ? (
              <div>
                {values.floatContents.map((content: any, index: number) => (
                  <Tag key={index} color="purple" style={{ marginBottom: 4 }}>
                    {content.title} ({content.type})
                  </Tag>
                ))}
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">共 {values.floatContents.length} 个浮点内容</Text>
                </div>
              </div>
            ) : (
              <Text type="secondary">暂无浮点内容配置</Text>
            )}
          </div>

          {/* 游戏分类组预览 */}
          <div style={{ marginBottom: 24 }}>
            <Title level={5}>游戏分类组配置</Title>
            {values.gameCategories && values.gameCategories.length > 0 ? (
              <div>
                {values.gameCategories.map((category: any, index: number) => (
                  <div key={index} style={{ marginBottom: 12, padding: 12, border: '1px solid #d9d9d9', borderRadius: 4 }}>
                    <div style={{ marginBottom: 8 }}>
                      <Tag color="blue">{category.categoryTitle?.zh}</Tag>
                      <Tag color="green">{category.categoryTitle?.en}</Tag>
                      <Tag>{category.games?.length || 0} 个游戏</Tag>
                    </div>
                    {category.games && category.games.length > 0 && (
                      <div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          游戏：{category.games.slice(0, 3).map((game: any) => getGameName(game.applicationId)).join(', ')}
                          {category.games.length > 3 && ` 等${category.games.length}个`}
                        </Text>
                      </div>
                    )}
                  </div>
                ))}
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">共 {values.gameCategories.length} 个分类组</Text>
                </div>
              </div>
            ) : (
              <Text type="secondary">暂无游戏分类组配置</Text>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default BoxModeForm;
