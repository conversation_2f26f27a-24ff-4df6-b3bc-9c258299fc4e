import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  Card,
  Typography,
  Divider,
  Space,
  Tag,
  Tabs,
  Alert,
  Button,
  Tooltip
} from 'antd';
import {
  AppstoreOutlined,
  PictureOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import GameCategoryConfig from './GameCategoryConfig';
import type { Application } from '#src/api/application';
import type { AdConfig } from '#src/api/config';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 表单数据接口
interface BoxModeFormData {
  configName: string;
  description?: string;
  templateType: string;
  status: number;
  sortOrder?: number;
  remark?: string;

  // 广告配置
  topBannerAdId?: number;
  carouselAdId?: number;
  homeGridAdId: number; // BOX模式必需4宫格广告
  splashPopupAdId?: number;
  floatAdId?: number;

  // BOX模式特有配置
  gameCategories: any[];
}

// 组件属性接口
interface BoxModeFormProps {
  form: any;
  adOptions: AdConfig[];
  applications: Application[];
  initialValues?: Partial<BoxModeFormData>;
  disabled?: boolean;
}

// 模板样式选项 - 不同的排版风格和视觉呈现
const TEMPLATE_OPTIONS = [
  { value: 'box', label: 'BOX风格', description: '盒子布局风格，支持4宫格、浮点内容、游戏分类组等' },
  { value: 'classic', label: '经典风格', description: '传统列表布局风格，简洁明了' },
  { value: 'card', label: '卡片风格', description: '卡片式布局，现代化设计风格' },
  { value: 'grid', label: '网格风格', description: '网格布局，整齐排列' }
];

// 状态选项
const STATUS_OPTIONS = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
];

const BoxModeForm: React.FC<BoxModeFormProps> = ({
  form,
  adOptions = [],
  applications = [],
  initialValues,
  disabled = false
}) => {
  const [templateType, setTemplateType] = useState<string>('box');
  const [activeTab, setActiveTab] = useState<string>('basic');

  // 广告类型过滤函数
  const getAdsByType = (adType: number) => {
    return adOptions.filter(ad => ad.adType === adType);
  };

  // 各类型广告选项
  const carouselAdOptions = getAdsByType(1);  // 轮播广告
  const popupAdOptions = getAdsByType(2);     // 弹窗广告
  const floatAdOptions = getAdsByType(3);     // 浮点弹窗广告
  const bannerAdOptions = getAdsByType(5);    // Banner广告
  const gridAdOptions = getAdsByType(7);      // 4宫格广告

  // 监听模板类型变化
  const handleTemplateTypeChange = (value: string) => {
    setTemplateType(value);
    form.setFieldValue('templateType', value);
  };

  // 获取验证状态
  const getValidationStatus = () => {
    const values = form.getFieldsValue();
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本信息验证
    if (!values.configName) {
      errors.push('配置名称不能为空');
    }

    if (templateType === 'box') {
      // BOX模式特殊验证
      if (!values.homeGridAdId) {
        errors.push('BOX模式必须选择4宫格广告');
      }

      // 游戏分类组验证
      const categories = values.gameCategories || [];
      if (categories.length < 4) {
        errors.push('BOX模式至少需要4个游戏分类组');
      }

      categories.forEach((cat: any, index: number) => {
        if (!cat.categoryTitle?.zh || !cat.categoryTitle?.en) {
          errors.push(`第${index + 1}个分类组标题不完整`);
        }
        if (!cat.games || cat.games.length < 4) {
          errors.push(`第${index + 1}个分类组至少需要4个游戏`);
        }
      });

      // 推荐配置提醒
      if (!values.topBannerAdId) {
        warnings.push('建议配置顶部Banner广告以提升用户体验');
      }
      if (!values.carouselAdId) {
        warnings.push('建议配置轮播广告以展示重要内容');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  };

  const validation = getValidationStatus();

  return (
    <div>
      {/* 验证状态提示 */}
      {!validation.isValid && (
        <Alert
          message="配置验证失败"
          description={
            <ul style={{ margin: '8px 0 0 20px' }}>
              {validation.errors.map((error, index) => (
                <li key={index} style={{ color: '#ff4d4f' }}>{error}</li>
              ))}
            </ul>
          }
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {validation.warnings.length > 0 && (
        <Alert
          message="配置建议"
          description={
            <ul style={{ margin: '8px 0 0 20px' }}>
              {validation.warnings.map((warning, index) => (
                <li key={index} style={{ color: '#faad14' }}>{warning}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
        {/* 基本配置 */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              基本配置
              {!validation.isValid && <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginLeft: 4 }} />}
            </span>
          }
          key="basic"
        >
          <Card size="small" title="基本信息">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="configName"
                  label="配置名称"
                  rules={[{ required: true, message: '请输入配置名称' }]}
                >
                  <Input placeholder="请输入配置名称" disabled={disabled} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="templateType"
                  label="模板样式"
                  rules={[{ required: true, message: '请选择模板样式' }]}
                >
                  <Select
                    placeholder="请选择模板样式"
                    onChange={handleTemplateTypeChange}
                    disabled={disabled}
                  >
                    {TEMPLATE_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        <div>
                          <div>{option.label}</div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {option.description}
                          </Text>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select placeholder="请选择状态" disabled={disabled}>
                    {STATUS_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="sortOrder"
                  label="排序"
                >
                  <InputNumber 
                    placeholder="请输入排序值" 
                    min={0} 
                    max={9999}
                    style={{ width: '100%' }}
                    disabled={disabled}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="配置描述"
            >
              <TextArea 
                placeholder="请输入配置描述" 
                rows={3}
                maxLength={500}
                showCount
                disabled={disabled}
              />
            </Form.Item>

            <Form.Item
              name="remark"
              label="备注"
            >
              <TextArea 
                placeholder="请输入备注" 
                rows={2}
                maxLength={500}
                showCount
                disabled={disabled}
              />
            </Form.Item>
          </Card>
        </TabPane>

        {/* 广告配置 */}
        <TabPane
          tab={
            <span>
              <PictureOutlined />
              广告配置
            </span>
          }
          key="ads"
        >
          <Card size="small" title="广告位配置">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="topBannerAdId"
                  label="顶部Banner广告"
                  extra="显示在页面顶部的Banner广告"
                >
                  <Select placeholder="请选择Banner广告" allowClear showSearch disabled={disabled}>
                    {bannerAdOptions.length > 0 ? bannerAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无Banner广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="carouselAdId"
                  label="轮播广告"
                  extra="首页顶部轮播展示的广告"
                >
                  <Select placeholder="请选择轮播广告" allowClear showSearch disabled={disabled}>
                    {carouselAdOptions.length > 0 ? carouselAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无轮播广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {templateType === 'box' && (
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="homeGridAdId"
                    label={
                      <span>
                        4宫格广告
                        <Tag color="red" style={{ marginLeft: 8 }}>BOX模式必需</Tag>
                      </span>
                    }
                    rules={[{ required: true, message: 'BOX模式必须选择4宫格广告' }]}
                    extra="BOX模式的核心广告位，展示4个重要功能入口"
                  >
                    <Select placeholder="请选择4宫格广告" showSearch disabled={disabled}>
                      {gridAdOptions.length > 0 ? gridAdOptions.map((ad: AdConfig) => (
                        <Option key={ad.id} value={ad.id}>
                          {ad.title} ({ad.adIdentifier})
                        </Option>
                      )) : (
                        <Option value="" disabled>
                          暂无4宫格广告，请先创建
                        </Option>
                      )}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            )}

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="splashPopupAdId"
                  label="开屏弹窗广告"
                  extra="APP启动时显示的弹窗广告"
                >
                  <Select placeholder="请选择弹窗广告" allowClear showSearch disabled={disabled}>
                    {popupAdOptions.length > 0 ? popupAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无弹窗广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="floatAdId"
                  label="浮点弹窗广告"
                  extra="页面浮动显示的弹窗广告"
                >
                  <Select placeholder="请选择浮点广告" allowClear showSearch disabled={disabled}>
                    {floatAdOptions.length > 0 ? floatAdOptions.map((ad: AdConfig) => (
                      <Option key={ad.id} value={ad.id}>
                        {ad.title} ({ad.adIdentifier})
                      </Option>
                    )) : (
                      <Option value="" disabled>
                        暂无浮点广告，请先创建
                      </Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>

            </Row>
          </Card>
        </TabPane>

        {/* 游戏分类组配置 */}
        {templateType === 'box' && (
          <TabPane
            tab={
              <span>
                <AppstoreOutlined />
                游戏分类配置
                <Tag color="green" style={{ marginLeft: 4 }}>BOX</Tag>
              </span>
            }
            key="categories"
          >
            <Card size="small" title="游戏分类组配置" extra={
              <Tooltip title="BOX模式至少需要4个游戏分类组，每个分类组至少4个游戏">
                <InfoCircleOutlined />
              </Tooltip>
            }>
              <Form.Item name="gameCategories" noStyle>
                <GameCategoryConfig
                  applications={applications}
                  disabled={disabled}
                />
              </Form.Item>
            </Card>
          </TabPane>
        )}
      </Tabs>
    </div>
  );
};



export default BoxModeForm;
