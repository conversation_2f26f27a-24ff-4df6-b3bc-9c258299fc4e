-- =============================================
-- APP首页配置表添加模板和浮点内容字段
-- =============================================

-- 添加模板类型字段
ALTER TABLE app_home_configs
ADD COLUMN IF NOT EXISTS template_type VARCHAR(50) DEFAULT 'box';

-- 添加浮点内容配置字段
ALTER TABLE app_home_configs
ADD COLUMN IF NOT EXISTS float_contents JSONB DEFAULT '[]'::jsonb;

-- 添加字段注释
COMMENT ON COLUMN app_home_configs.template_type IS '首页模板类型：box-BOX模式，classic-经典模式，card-卡片模式，waterfall-瀑布流模式';
COMMENT ON COLUMN app_home_configs.float_contents IS '浮点内容配置数组，包含公告、活动、客服等浮点内容';
COMMENT ON COLUMN app_home_configs.home_grid_ad_id IS '首页6宫格广告ID（BOX模式专用）';

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_app_home_configs_template_type ON app_home_configs(template_type);
CREATE INDEX IF NOT EXISTS idx_app_home_configs_float_contents ON app_home_configs USING GIN(float_contents);

-- 添加约束检查
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_template_type'
        AND table_name = 'app_home_configs'
    ) THEN
        ALTER TABLE app_home_configs
        ADD CONSTRAINT chk_template_type
        CHECK (template_type IN ('box', 'classic', 'card', 'waterfall'));
    END IF;
END $$;

-- 更新现有记录的模板类型为BOX模式
UPDATE app_home_configs SET template_type = 'box' WHERE template_type IS NULL;
