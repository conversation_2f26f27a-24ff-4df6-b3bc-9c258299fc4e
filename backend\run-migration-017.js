const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  const client = new Client({
    host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
    port: 5432,
    user: 'postgres',
    password: 'inapp2backend2024!',
    database: 'postgres'
  });

  try {
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 读取迁移文件
    const migrationPath = path.join(__dirname, 'migrations', '017-remove-float-fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📋 执行迁移: 017-remove-float-fields.sql');
    console.log('SQL内容:');
    console.log(migrationSQL);

    // 执行迁移
    await client.query(migrationSQL);
    console.log('✅ 迁移执行成功');

    // 验证字段是否已删除
    const result = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'app_home_configs' 
      AND column_name IN ('float_ad_id', 'float_contents')
    `);

    if (result.rows.length === 0) {
      console.log('✅ 确认：浮点相关字段已成功删除');
    } else {
      console.log('⚠️ 警告：以下字段仍然存在:', result.rows.map(r => r.column_name));
    }

  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    process.exit(1);
  } finally {
    await client.end();
    console.log('📋 数据库连接已关闭');
  }
}

runMigration();
