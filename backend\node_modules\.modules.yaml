hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@angular-devkit/core@19.2.8(chokidar@4.0.3)':
    '@angular-devkit/core': private
  '@angular-devkit/schematics-cli@19.2.8(@types/node@22.15.32)(chokidar@4.0.3)':
    '@angular-devkit/schematics-cli': private
  '@angular-devkit/schematics@19.2.8(chokidar@4.0.3)':
    '@angular-devkit/schematics': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.5':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.4)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.4)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.4)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.4)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.4)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.4)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.4)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.4)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.4)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.4)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.4)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.4)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.4)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-typescript': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.2':
    '@eslint/plugin-kit': private
  '@gar/promisify@1.1.3':
    '@gar/promisify': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@inquirer/checkbox@4.1.8(@types/node@22.15.32)':
    '@inquirer/checkbox': private
  '@inquirer/confirm@5.1.12(@types/node@22.15.32)':
    '@inquirer/confirm': private
  '@inquirer/core@10.1.13(@types/node@22.15.32)':
    '@inquirer/core': private
  '@inquirer/editor@4.2.13(@types/node@22.15.32)':
    '@inquirer/editor': private
  '@inquirer/expand@4.0.15(@types/node@22.15.32)':
    '@inquirer/expand': private
  '@inquirer/figures@1.0.12':
    '@inquirer/figures': private
  '@inquirer/input@4.1.12(@types/node@22.15.32)':
    '@inquirer/input': private
  '@inquirer/number@3.0.15(@types/node@22.15.32)':
    '@inquirer/number': private
  '@inquirer/password@4.0.15(@types/node@22.15.32)':
    '@inquirer/password': private
  '@inquirer/prompts@7.4.1(@types/node@22.15.32)':
    '@inquirer/prompts': private
  '@inquirer/rawlist@4.1.3(@types/node@22.15.32)':
    '@inquirer/rawlist': private
  '@inquirer/search@3.0.15(@types/node@22.15.32)':
    '@inquirer/search': private
  '@inquirer/select@4.2.3(@types/node@22.15.32)':
    '@inquirer/select': private
  '@inquirer/type@3.0.7(@types/node@22.15.32)':
    '@inquirer/type': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(ts-node@10.9.2(@swc/core@1.12.1)(@types/node@22.15.32)(typescript@5.8.3))':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': private
  '@napi-rs/nice-android-arm-eabi@1.0.1':
    '@napi-rs/nice-android-arm-eabi': private
  '@napi-rs/nice-android-arm64@1.0.1':
    '@napi-rs/nice-android-arm64': private
  '@napi-rs/nice-darwin-arm64@1.0.1':
    '@napi-rs/nice-darwin-arm64': private
  '@napi-rs/nice-darwin-x64@1.0.1':
    '@napi-rs/nice-darwin-x64': private
  '@napi-rs/nice-freebsd-x64@1.0.1':
    '@napi-rs/nice-freebsd-x64': private
  '@napi-rs/nice-linux-arm-gnueabihf@1.0.1':
    '@napi-rs/nice-linux-arm-gnueabihf': private
  '@napi-rs/nice-linux-arm64-gnu@1.0.1':
    '@napi-rs/nice-linux-arm64-gnu': private
  '@napi-rs/nice-linux-arm64-musl@1.0.1':
    '@napi-rs/nice-linux-arm64-musl': private
  '@napi-rs/nice-linux-ppc64-gnu@1.0.1':
    '@napi-rs/nice-linux-ppc64-gnu': private
  '@napi-rs/nice-linux-riscv64-gnu@1.0.1':
    '@napi-rs/nice-linux-riscv64-gnu': private
  '@napi-rs/nice-linux-s390x-gnu@1.0.1':
    '@napi-rs/nice-linux-s390x-gnu': private
  '@napi-rs/nice-linux-x64-gnu@1.0.1':
    '@napi-rs/nice-linux-x64-gnu': private
  '@napi-rs/nice-linux-x64-musl@1.0.1':
    '@napi-rs/nice-linux-x64-musl': private
  '@napi-rs/nice-win32-arm64-msvc@1.0.1':
    '@napi-rs/nice-win32-arm64-msvc': private
  '@napi-rs/nice-win32-ia32-msvc@1.0.1':
    '@napi-rs/nice-win32-ia32-msvc': private
  '@napi-rs/nice-win32-x64-msvc@1.0.1':
    '@napi-rs/nice-win32-x64-msvc': private
  '@napi-rs/nice@1.0.1':
    '@napi-rs/nice': private
  '@nestjs/mapped-types@2.1.0(@nestjs/common@11.1.3(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)':
    '@nestjs/mapped-types': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@npmcli/fs@1.1.1':
    '@npmcli/fs': private
  '@npmcli/move-file@1.1.2':
    '@npmcli/move-file': private
  '@nuxt/opencollective@0.4.1':
    '@nuxt/opencollective': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.7':
    '@pkgr/core': private
  '@scarf/scarf@1.4.0':
    '@scarf/scarf': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sindresorhus/is@5.6.0':
    '@sindresorhus/is': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@sqltools/formatter@1.2.5':
    '@sqltools/formatter': private
  '@swc/core-darwin-arm64@1.12.1':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.12.1':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.12.1':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.12.1':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.12.1':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.12.1':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.12.1':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.12.1':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.12.1':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.12.1':
    '@swc/core-win32-x64-msvc': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.23':
    '@swc/types': private
  '@szmarczak/http-timer@5.0.1':
    '@szmarczak/http-timer': private
  '@tokenizer/inflate@0.2.7':
    '@tokenizer/inflate': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cookiejar@2.1.5':
    '@types/cookiejar': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/hoist-non-react-statics@3.3.6':
    '@types/hoist-non-react-statics': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/jsonwebtoken@9.0.7':
    '@types/jsonwebtoken': private
  '@types/methods@1.1.4':
    '@types/methods': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/passport-strategy@0.2.38':
    '@types/passport-strategy': private
  '@types/passport@1.0.17':
    '@types/passport': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-redux@7.1.34':
    '@types/react-redux': private
  '@types/react@19.1.8':
    '@types/react': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/superagent@8.1.9':
    '@types/superagent': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/eslint-plugin@8.34.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0)(typescript@5.8.3))(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.34.1(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.34.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.34.1(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.34.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.34.1(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.34.1':
    '@typescript-eslint/visitor-keys': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xhmikosr/archive-type@7.0.0':
    '@xhmikosr/archive-type': private
  '@xhmikosr/bin-check@7.0.3':
    '@xhmikosr/bin-check': private
  '@xhmikosr/bin-wrapper@13.0.5':
    '@xhmikosr/bin-wrapper': private
  '@xhmikosr/decompress-tar@8.0.1':
    '@xhmikosr/decompress-tar': private
  '@xhmikosr/decompress-tarbz2@8.0.2':
    '@xhmikosr/decompress-tarbz2': private
  '@xhmikosr/decompress-targz@8.0.1':
    '@xhmikosr/decompress-targz': private
  '@xhmikosr/decompress-unzip@7.0.0':
    '@xhmikosr/decompress-unzip': private
  '@xhmikosr/decompress@10.0.1':
    '@xhmikosr/decompress': private
  '@xhmikosr/downloader@15.0.1':
    '@xhmikosr/downloader': private
  '@xhmikosr/os-filter-obj@3.0.0':
    '@xhmikosr/os-filter-obj': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abbrev@1.1.1:
    abbrev: private
  accepts@2.0.0:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@3.0.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@3.17.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  app-root-path@3.1.0:
    app-root-path: private
  append-field@1.0.0:
    append-field: private
  aproba@2.0.0:
    aproba: private
  arch@3.0.0:
    arch: private
  are-we-there-yet@3.0.1:
    are-we-there-yet: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-timsort@1.0.3:
    array-timsort: private
  asap@2.0.6:
    asap: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  b4a@1.6.7:
    b4a: private
  babel-jest@29.7.0(@babel/core@7.27.4):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.4):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.27.4):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  base64-js@1.5.1:
    base64-js: private
  bin-version-check@5.1.0:
    bin-version-check: private
  bin-version@6.0.0:
    bin-version: private
  bindings@1.5.0:
    bindings: private
  bl@4.1.0:
    bl: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@4.9.2:
    buffer: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  cacache@15.3.0:
    cacache: private
  cacheable-lookup@7.0.0:
    cacheable-lookup: private
  cacheable-request@10.2.14:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001723:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chardet@0.7.0:
    chardet: private
  chokidar@4.0.3:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-width@4.1.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@4.1.1:
    commander: private
  comment-json@4.2.5:
    comment-json: private
  component-emitter@1.3.1:
    component-emitter: private
  compressible@2.0.18:
    compressible: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  consola@3.4.2:
    consola: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookiejar@2.1.4:
    cookiejar: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cosmiconfig@8.3.6(typescript@5.8.3):
    cosmiconfig: private
  create-jest@29.7.0(@types/node@22.15.32)(ts-node@10.9.2(@swc/core@1.12.1)(@types/node@22.15.32)(typescript@5.8.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-box-model@1.2.1:
    css-box-model: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  dedent@1.6.0:
    dedent: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@3.0.0:
    defaults: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  dezalgo@1.0.4:
    dezalgo: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dotenv-expand@12.0.1:
    dotenv-expand: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.168:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding@0.1.13:
    encoding: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  env-paths@2.2.1:
    env-paths: private
  err-code@2.0.3:
    err-code: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  events@1.1.1:
    events: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expand-template@2.0.3:
    expand-template: private
  expect@29.7.0:
    expect: private
  express@5.1.0:
    express: private
  ext-list@2.2.2:
    ext-list: private
  ext-name@5.0.0:
    ext-name: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fflate@0.8.2:
    fflate: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-type@21.0.0:
    file-type: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  filelist@1.0.4:
    filelist: private
  filename-reserved-regex@3.0.0:
    filename-reserved-regex: private
  filenamify@6.0.0:
    filenamify: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  find-versions@5.1.0:
    find-versions: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  fork-ts-checker-webpack-plugin@9.1.0(typescript@5.8.3)(webpack@5.99.6(@swc/core@1.12.1)):
    fork-ts-checker-webpack-plugin: private
  form-data-encoder@2.1.4:
    form-data-encoder: private
  form-data@4.0.3:
    form-data: private
  formidable@3.5.4:
    formidable: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs-monkey@1.0.6:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gauge@4.0.4:
    gauge: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@11.0.1:
    glob: private
  gopd@1.2.0:
    gopd: private
  got@13.0.0:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-own-prop@2.0.0:
    has-own-prop: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  html-escaper@2.0.2:
    html-escaper: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@4.0.1:
    http-proxy-agent: private
  http2-wrapper@2.2.1:
    http2-wrapper: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.1.13:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  infer-owner@1.0.4:
    infer-owner: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inspect-with-kind@1.0.5:
    inspect-with-kind: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arguments@1.2.0:
    is-arguments: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lambda@1.0.1:
    is-lambda: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-stream@2.0.1:
    is-stream: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterare@1.2.1:
    iterare: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@22.15.32)(ts-node@10.9.2(@swc/core@1.12.1)(@types/node@22.15.32)(typescript@5.8.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@22.15.32)(ts-node@10.9.2(@swc/core@1.12.1)(@types/node@22.15.32)(typescript@5.8.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jmespath@0.16.0:
    jmespath: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@3.0.3:
    kleur: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  libphonenumber-js@1.12.9:
    libphonenumber-js: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-esm@1.0.2:
    load-esm: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  loose-envify@1.4.0:
    loose-envify: private
  lowercase-keys@3.0.0:
    lowercase-keys: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  make-fetch-happen@9.1.0:
    make-fetch-happen: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  memfs@3.5.3:
    memfs: private
  memoize-one@5.2.1:
    memoize-one: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@4.0.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass-collect@1.0.2:
    minipass-collect: private
  minipass-fetch@1.4.1:
    minipass-fetch: private
  minipass-flush@1.0.5:
    minipass-flush: private
  minipass-pipeline@1.2.4:
    minipass-pipeline: private
  minipass-sized@1.0.3:
    minipass-sized: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.0.0:
    ms: private
  multer@2.0.1:
    multer: private
  mute-stream@2.0.0:
    mute-stream: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  node-abi@3.75.0:
    node-abi: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-emoji@1.11.0:
    node-emoji: private
  node-gyp@8.4.1:
    node-gyp: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  nopt@5.0.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-url@8.0.2:
    normalize-url: private
  npm-run-path@4.0.1:
    npm-run-path: private
  npmlog@6.0.2:
    npmlog: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-cancelable@3.0.0:
    p-cancelable: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  passport-strategy@1.0.0:
    passport-strategy: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pause@0.0.1:
    pause: private
  peek-readable@5.4.2:
    peek-readable: private
  pend@1.2.0:
    pend: private
  pg-cloudflare@1.2.5:
    pg-cloudflare: private
  pg-connection-string@2.9.0:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.0(pg@8.16.0):
    pg-pool: private
  pg-protocol@1.10.0:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  piscina@4.9.2:
    piscina: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pluralize@8.0.0:
    pluralize: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prebuild-install@7.1.3:
    prebuild-install: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  pretty-format@29.7.0:
    pretty-format: private
  promise-inflight@1.0.1:
    promise-inflight: private
  promise-retry@2.0.1:
    promise-retry: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pump@3.0.3:
    pump: private
  punycode@1.3.2:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.14.0:
    qs: private
  querystring@0.2.0:
    querystring: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  raf-schd@4.0.3:
    raf-schd: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  rc@1.2.8:
    rc: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-is@18.3.1:
    react-is: private
  react-redux@7.2.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-redux: private
  react@18.3.1:
    react: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  redux@4.2.1:
    redux: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  responselike@3.0.0:
    responselike: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry@0.12.0:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  router@2.2.0:
    router: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.2.1:
    sax: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@3.3.0:
    schema-utils: private
  seek-bzip@2.0.0:
    seek-bzip: private
  semver-regex@4.0.5:
    semver-regex: private
  semver-truncate@3.0.0:
    semver-truncate: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-static@2.2.0:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.11:
    sha.js: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@6.2.1:
    socks-proxy-agent: private
  socks@2.8.5:
    socks: private
  sort-keys-length@1.0.1:
    sort-keys-length: private
  sort-keys@1.1.2:
    sort-keys: private
  source-map@0.7.4:
    source-map: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  sql-highlight@6.1.0:
    sql-highlight: private
  sqlite3@5.1.7:
    sqlite3: private
  ssri@8.0.1:
    ssri: private
  stack-utils@2.0.6:
    stack-utils: private
  statuses@2.0.2:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  streamx@2.22.1:
    streamx: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-dirs@3.0.0:
    strip-dirs: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strtok3@10.3.1:
    strtok3: private
  superagent@10.2.1:
    superagent: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swagger-ui-dist@5.21.0:
    swagger-ui-dist: private
  symbol-observable@4.0.0:
    symbol-observable: private
  synckit@0.11.8:
    synckit: private
  tapable@2.2.2:
    tapable: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@6.2.1:
    tar: private
  terser-webpack-plugin@5.3.14(@swc/core@1.12.1)(webpack@5.99.6(@swc/core@1.12.1)):
    terser-webpack-plugin: private
  terser@5.42.0:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  text-decoder@1.2.3:
    text-decoder: private
  through@2.3.8:
    through: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tmp@0.0.33:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@6.0.0:
    token-types: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tsconfig-paths-webpack-plugin@4.2.0:
    tsconfig-paths-webpack-plugin: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  type-is@2.0.1:
    type-is: private
  typedarray@0.0.6:
    typedarray: private
  uid@2.0.2:
    uid: private
  uint8array-extras@1.4.0:
    uint8array-extras: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  undici-types@6.21.0:
    undici-types: private
  unique-filename@1.1.1:
    unique-filename: private
  unique-slug@2.0.2:
    unique-slug: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url@0.10.3:
    url: private
  use-memo-one@1.1.3(react@18.3.1):
    use-memo-one: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.0.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  walker@1.0.8:
    walker: private
  watchpack@2.4.4:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  webpack-node-externals@3.0.0:
    webpack-node-externals: private
  webpack-sources@3.3.2:
    webpack-sources: private
  webpack@5.99.6(@swc/core@1.12.1):
    webpack: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wide-align@1.1.5:
    wide-align: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  xml2js@0.6.2:
    xml2js: private
  xmlbuilder@11.0.1:
    xmlbuilder: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@3.2.0:
    yauzl: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: private
ignoredBuilds:
  - aws-sdk
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Thu, 26 Jun 2025 13:18:13 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@napi-rs/nice-android-arm-eabi@1.0.1'
  - '@napi-rs/nice-android-arm64@1.0.1'
  - '@napi-rs/nice-darwin-arm64@1.0.1'
  - '@napi-rs/nice-darwin-x64@1.0.1'
  - '@napi-rs/nice-freebsd-x64@1.0.1'
  - '@napi-rs/nice-linux-arm-gnueabihf@1.0.1'
  - '@napi-rs/nice-linux-arm64-gnu@1.0.1'
  - '@napi-rs/nice-linux-arm64-musl@1.0.1'
  - '@napi-rs/nice-linux-ppc64-gnu@1.0.1'
  - '@napi-rs/nice-linux-riscv64-gnu@1.0.1'
  - '@napi-rs/nice-linux-s390x-gnu@1.0.1'
  - '@napi-rs/nice-linux-x64-gnu@1.0.1'
  - '@napi-rs/nice-linux-x64-musl@1.0.1'
  - '@napi-rs/nice-win32-arm64-msvc@1.0.1'
  - '@napi-rs/nice-win32-ia32-msvc@1.0.1'
  - '@swc/core-darwin-arm64@1.12.1'
  - '@swc/core-darwin-x64@1.12.1'
  - '@swc/core-linux-arm-gnueabihf@1.12.1'
  - '@swc/core-linux-arm64-gnu@1.12.1'
  - '@swc/core-linux-arm64-musl@1.12.1'
  - '@swc/core-linux-x64-gnu@1.12.1'
  - '@swc/core-linux-x64-musl@1.12.1'
  - '@swc/core-win32-arm64-msvc@1.12.1'
  - '@swc/core-win32-ia32-msvc@1.12.1'
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\inwork\inapp\backend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
