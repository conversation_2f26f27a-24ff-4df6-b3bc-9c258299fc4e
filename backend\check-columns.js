const { Pool } = require('pg');

// 数据库配置
const pool = new Pool({
  host: '**************',
  port: 5435,
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
  database: 'inapp2',
});

async function checkColumns() {
  try {
    // 查询app_home_configs表的列信息
    const result = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'app_home_configs' 
      ORDER BY ordinal_position;
    `);
    
    console.log('app_home_configs表的列信息:');
    console.table(result.rows);
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    await pool.end();
  }
}

checkColumns();
