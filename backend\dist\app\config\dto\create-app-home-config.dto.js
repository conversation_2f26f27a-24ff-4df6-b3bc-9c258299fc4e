"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppHomeConfigDto = exports.GameCategoryDto = exports.CategoryGameDto = exports.RecommendedGameDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class RecommendedGameDto {
    applicationId;
    sortOrder;
}
exports.RecommendedGameDto = RecommendedGameDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏应用ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '游戏应用ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '游戏应用ID必须是数字' }),
    __metadata("design:type", Number)
], RecommendedGameDto.prototype, "applicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序，数字越小越靠前', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序不能小于0' }),
    (0, class_validator_1.Max)(9999, { message: '排序不能大于9999' }),
    __metadata("design:type", Number)
], RecommendedGameDto.prototype, "sortOrder", void 0);
class CategoryGameDto {
    applicationId;
    sortOrder;
}
exports.CategoryGameDto = CategoryGameDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏应用ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '游戏应用ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '游戏应用ID必须是数字' }),
    __metadata("design:type", Number)
], CategoryGameDto.prototype, "applicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序，数字越小越靠前', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序不能小于0' }),
    (0, class_validator_1.Max)(9999, { message: '排序不能大于9999' }),
    __metadata("design:type", Number)
], CategoryGameDto.prototype, "sortOrder", void 0);
class GameCategoryDto {
    categoryTitle;
    sortOrder;
    games;
}
exports.GameCategoryDto = GameCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分类标题（多语言支持）',
        example: { "zh-CN": "热门游戏", "en-US": "Hot Games" }
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '分类标题不能为空' }),
    (0, class_validator_1.IsObject)({ message: '分类标题必须是对象格式' }),
    __metadata("design:type", Object)
], GameCategoryDto.prototype, "categoryTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序，数字越小越靠前', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序不能小于0' }),
    (0, class_validator_1.Max)(9999, { message: '排序不能大于9999' }),
    __metadata("design:type", Number)
], GameCategoryDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分类下的游戏列表（至少4个）',
        type: [CategoryGameDto],
        minItems: 4
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '分类游戏列表不能为空' }),
    (0, class_validator_1.IsArray)({ message: '分类游戏必须是数组格式' }),
    (0, class_validator_1.ArrayMinSize)(4, { message: '每个分类至少需要4个游戏' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CategoryGameDto),
    __metadata("design:type", Array)
], GameCategoryDto.prototype, "games", void 0);
class CreateAppHomeConfigDto {
    configName;
    description;
    topBannerAdId;
    carouselAdId;
    homeGridAdId;
    splashPopupAdId;
    templateType;
    recommendedGames;
    gameCategories;
    sortOrder;
    status;
    remark;
}
exports.CreateAppHomeConfigDto = CreateAppHomeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置名称',
        example: '默认首页配置'
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '配置名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '配置名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '配置名称长度必须在1-100个字符之间' }),
    __metadata("design:type", String)
], CreateAppHomeConfigDto.prototype, "configName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置描述',
        example: '系统默认的APP首页配置',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '配置描述必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '配置描述长度不能超过500个字符' }),
    __metadata("design:type", String)
], CreateAppHomeConfigDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '顶部Banner广告ID（必填）',
        example: 1
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '顶部Banner广告ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '顶部Banner广告ID必须是数字' }),
    __metadata("design:type", Number)
], CreateAppHomeConfigDto.prototype, "topBannerAdId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轮播广告ID（必填）',
        example: 2
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '轮播广告ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '轮播广告ID必须是数字' }),
    __metadata("design:type", Number)
], CreateAppHomeConfigDto.prototype, "carouselAdId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '首页6宫格广告ID（BOX模式专用）',
        example: 3,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '首页6宫格广告ID必须是数字' }),
    __metadata("design:type", Number)
], CreateAppHomeConfigDto.prototype, "homeGridAdId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '开屏弹窗广告ID（可选）',
        example: 4,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '开屏弹窗广告ID必须是数字' }),
    __metadata("design:type", Number)
], CreateAppHomeConfigDto.prototype, "splashPopupAdId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '首页模板类型',
        example: 'box',
        enum: ['box', 'classic', 'card', 'waterfall'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['box', 'classic', 'card', 'waterfall'], { message: '模板类型必须是有效值' }),
    __metadata("design:type", String)
], CreateAppHomeConfigDto.prototype, "templateType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推荐游戏列表（必须6个）',
        type: [RecommendedGameDto],
        minItems: 6,
        maxItems: 6
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '推荐游戏列表不能为空' }),
    (0, class_validator_1.IsArray)({ message: '推荐游戏必须是数组格式' }),
    (0, class_validator_1.ArrayMinSize)(6, { message: '必须配置6个推荐游戏' }),
    (0, class_validator_1.ArrayMaxSize)(6, { message: '推荐游戏不能超过6个' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => RecommendedGameDto),
    __metadata("design:type", Array)
], CreateAppHomeConfigDto.prototype, "recommendedGames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '游戏分类组列表（至少4个分类）',
        type: [GameCategoryDto],
        minItems: 4
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '游戏分类组列表不能为空' }),
    (0, class_validator_1.IsArray)({ message: '游戏分类组必须是数组格式' }),
    (0, class_validator_1.ArrayMinSize)(4, { message: '至少需要4个游戏分类组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => GameCategoryDto),
    __metadata("design:type", Array)
], CreateAppHomeConfigDto.prototype, "gameCategories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序，数字越小越靠前',
        example: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序不能小于0' }),
    (0, class_validator_1.Max)(9999, { message: '排序不能大于9999' }),
    __metadata("design:type", Number)
], CreateAppHomeConfigDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态：1-启用，0-禁用',
        example: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateAppHomeConfigDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '备注说明',
        example: '默认配置，用于APP首页展示',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '备注长度不能超过500个字符' }),
    __metadata("design:type", String)
], CreateAppHomeConfigDto.prototype, "remark", void 0);
//# sourceMappingURL=create-app-home-config.dto.js.map