/**
 * 简单的上传功能测试脚本
 * 在浏览器控制台中运行以验证上传功能
 */

// 测试配置加载
console.log('=== 测试上传配置 ===');
try {
  // 检查环境变量
  const config = {
    environment: import.meta?.env?.VITE_ENVIRONMENT || 'unknown',
    supabaseUrl: import.meta?.env?.VITE_SUPABASE_URL || 'not set',
    bucket: import.meta?.env?.VITE_SUPABASE_S3_BUCKET || 'not set',
    folder: import.meta?.env?.VITE_SUPABASE_S3_FOLDER || 'not set'
  };
  
  console.log('配置信息:', config);
  
  // 检查是否有缺失的配置
  const missingConfigs = Object.entries(config)
    .filter(([key, value]) => value === 'not set' || value === 'unknown')
    .map(([key]) => key);
    
  if (missingConfigs.length > 0) {
    console.warn('缺失的配置项:', missingConfigs);
  } else {
    console.log('✅ 所有配置项都已设置');
  }
} catch (error) {
  console.error('❌ 配置检查失败:', error);
}

// 测试模块导入
console.log('\n=== 测试模块导入 ===');
try {
  // 这个测试需要在实际的应用环境中运行
  console.log('请在浏览器开发者工具中运行以下代码来测试模块导入:');
  console.log(`
// 测试导入上传模块
import('#src/utils/supabase-upload').then(module => {
  console.log('✅ 上传模块导入成功');
  console.log('可用函数:', Object.keys(module));
  
  // 测试配置模块
  return import('#src/utils/upload-config');
}).then(configModule => {
  console.log('✅ 配置模块导入成功');
  console.log('配置验证结果:', configModule.configValidation);
}).catch(error => {
  console.error('❌ 模块导入失败:', error);
});
  `);
} catch (error) {
  console.error('❌ 模块导入测试失败:', error);
}

// 创建测试图片文件的辅助函数
console.log('\n=== 创建测试图片 ===');
console.log(`
// 在浏览器控制台中运行以下代码创建测试图片:
function createTestImage() {
  const canvas = document.createElement('canvas');
  canvas.width = 100;
  canvas.height = 100;
  const ctx = canvas.getContext('2d');
  
  // 绘制一个简单的测试图片
  ctx.fillStyle = '#ff0000';
  ctx.fillRect(0, 0, 50, 50);
  ctx.fillStyle = '#00ff00';
  ctx.fillRect(50, 0, 50, 50);
  ctx.fillStyle = '#0000ff';
  ctx.fillRect(0, 50, 50, 50);
  ctx.fillStyle = '#ffff00';
  ctx.fillRect(50, 50, 50, 50);
  
  return new Promise(resolve => {
    canvas.toBlob(blob => {
      const file = new File([blob], 'test-image.png', { type: 'image/png' });
      resolve(file);
    }, 'image/png');
  });
}

// 使用示例:
createTestImage().then(file => {
  console.log('测试图片创建成功:', file);
  // 现在可以使用这个文件测试上传功能
});
`);

// 上传测试示例
console.log('\n=== 上传测试示例 ===');
console.log(`
// 完整的上传测试示例:
async function testUpload() {
  try {
    // 1. 导入上传模块
    const uploadModule = await import('#src/utils/supabase-upload');
    console.log('✅ 上传模块导入成功');
    
    // 2. 创建测试图片
    const testFile = await createTestImage();
    console.log('✅ 测试图片创建成功:', testFile);
    
    // 3. 测试单个上传
    console.log('开始测试单个图片上传...');
    const imageUrl = await uploadModule.uploadImageToSupabase(testFile, 'test');
    console.log('✅ 单个上传成功:', imageUrl);
    
    // 4. 测试压缩上传
    console.log('开始测试压缩上传...');
    const compressedUrl = await uploadModule.uploadCompressedImageToSupabase(testFile, 'test', {
      maxWidth: 50,
      maxHeight: 50,
      quality: 0.8
    });
    console.log('✅ 压缩上传成功:', compressedUrl);
    
    // 5. 测试批量上传
    console.log('开始测试批量上传...');
    const files = [testFile, testFile]; // 使用同一个文件测试
    const results = await uploadModule.uploadMultipleImagesToSupabase(files, 'test', 
      (progress, current, total) => {
        console.log(\`上传进度: \${progress.toFixed(1)}% (\${current}/\${total})\`);
      }
    );
    console.log('✅ 批量上传完成:', results);
    
    return {
      singleUpload: imageUrl,
      compressedUpload: compressedUrl,
      batchUpload: results
    };
    
  } catch (error) {
    console.error('❌ 上传测试失败:', error);
    throw error;
  }
}

// 运行测试
testUpload().then(results => {
  console.log('🎉 所有上传测试通过!', results);
}).catch(error => {
  console.error('💥 上传测试失败:', error);
});
`);

console.log('\n=== 测试说明 ===');
console.log('1. 确保前端服务器正在运行 (http://localhost:3333)');
console.log('2. 确保环境变量已正确配置');
console.log('3. 在浏览器开发者工具的控制台中运行上述测试代码');
console.log('4. 检查控制台输出和网络请求');
console.log('5. 验证图片是否成功上传到Supabase存储');
