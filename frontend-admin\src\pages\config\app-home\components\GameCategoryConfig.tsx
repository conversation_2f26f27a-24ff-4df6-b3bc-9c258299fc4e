import React, { useState, useEffect } from 'react';
import type { Key } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Row,
  Col,
  Typography,
  Divider,
  Tag,
  Tooltip,
  Modal,
  message,
  Collapse,
  Transfer,
  List,
  Avatar,
  Checkbox,
  Empty
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  DragOutlined,
  EditOutlined,
  PlayCircleOutlined,
  AppstoreOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import type { Application } from '#src/api/application';

const { Option } = Select;
const { Text, Title } = Typography;
const { Panel } = Collapse;

// 分类标题接口
interface CategoryTitle {
  zh: string;
  en: string;
  [key: string]: string;
}

// 游戏分类组接口
interface GameCategory {
  id?: number;
  categoryTitle: CategoryTitle;
  games: {
    applicationId: number;
    sortOrder: number;
  }[];
  sortOrder: number;
  status?: number;
}

// 组件属性接口
interface GameCategoryConfigProps {
  value?: GameCategory[];
  onChange?: (categories: GameCategory[]) => void;
  applications: Application[];
  disabled?: boolean;
}

// 游戏选择模态框组件
const GameSelectionModal: React.FC<{
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedGames: Application[]) => void;
  applications: Application[];
  selectedGameIds: number[];
}> = ({ visible, onCancel, onOk, applications, selectedGameIds }) => {
  const [targetKeys, setTargetKeys] = useState<Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);

  useEffect(() => {
    setTargetKeys(selectedGameIds.map(id => id.toString()));
  }, [selectedGameIds]);

  const handleChange = (nextTargetKeys: Key[]) => {
    setTargetKeys(nextTargetKeys);
  };

  const handleSelectChange = (sourceSelectedKeys: Key[], targetSelectedKeys: Key[]) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  const handleOk = () => {
    const selectedGames = applications.filter(app =>
      targetKeys.includes(app.id.toString())
    );
    onOk(selectedGames);
  };

  const renderItem = (item: Application) => {
    return {
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Avatar 
            src={item.iconUrl} 
            size="small" 
            icon={<PlayCircleOutlined />}
          />
          <div>
            <div>{item.name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {item.categories?.join(', ')}
            </Text>
          </div>
        </div>
      ),
      value: item.id.toString(),
    };
  };

  const dataSource = applications.map(renderItem);

  return (
    <Modal
      title="选择游戏"
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      width={800}
      okText="确定"
      cancelText="取消"
    >
      <Transfer
        dataSource={dataSource}
        titles={['可选游戏', '已选游戏']}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        onChange={handleChange}
        onSelectChange={handleSelectChange}
        render={item => item.label}
        listStyle={{
          width: 350,
          height: 400,
        }}
        showSearch
        filterOption={(inputValue, item) =>
          item.label.props.children[1].props.children[0].props.children
            .toLowerCase()
            .includes(inputValue.toLowerCase())
        }
      />
    </Modal>
  );
};

// 单个分类组配置组件
const CategoryItem: React.FC<{
  category: GameCategory;
  index: number;
  applications: Application[];
  onUpdate: (index: number, category: GameCategory) => void;
  onDelete: (index: number) => void;
  disabled?: boolean;
}> = ({ category, index, applications, onUpdate, onDelete, disabled }) => {
  const [gameModalVisible, setGameModalVisible] = useState(false);
  const [editingTitle, setEditingTitle] = useState(false);
  const [titleForm] = Form.useForm();

  // 获取已选游戏信息
  const selectedGames = category.games
    .map(game => applications.find(app => app.id === game.applicationId))
    .filter(Boolean) as Application[];

  const handleTitleEdit = () => {
    titleForm.setFieldsValue(category.categoryTitle);
    setEditingTitle(true);
  };

  const handleTitleSave = async () => {
    try {
      const values = await titleForm.validateFields();
      onUpdate(index, {
        ...category,
        categoryTitle: values
      });
      setEditingTitle(false);
    } catch (error) {
      console.error('标题验证失败:', error);
    }
  };

  const handleGameSelection = (selectedGames: Application[]) => {
    const games = selectedGames.map((game, idx) => ({
      applicationId: game.id,
      sortOrder: idx + 1
    }));
    
    onUpdate(index, {
      ...category,
      games
    });
    setGameModalVisible(false);
  };

  const handleGameReorder = (result: any) => {
    if (!result.destination) return;

    const newGames = Array.from(category.games);
    const [reorderedItem] = newGames.splice(result.source.index, 1);
    newGames.splice(result.destination.index, 0, reorderedItem);

    // 更新排序
    const updatedGames = newGames.map((game, idx) => ({
      ...game,
      sortOrder: idx + 1
    }));

    onUpdate(index, {
      ...category,
      games: updatedGames
    });
  };

  return (
    <Card
      size="small"
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <AppstoreOutlined />
            <span>分类组 {index + 1}</span>
            <Tag color={category.games.length >= 4 ? 'green' : 'orange'}>
              {category.games.length} 个游戏
            </Tag>
          </div>
          <Space>
            <Tooltip title="编辑标题">
              <Button 
                type="text" 
                size="small" 
                icon={<EditOutlined />}
                onClick={handleTitleEdit}
                disabled={disabled}
              />
            </Tooltip>
            <Tooltip title="删除分类组">
              <Button 
                type="text" 
                size="small" 
                danger
                icon={<DeleteOutlined />}
                onClick={() => onDelete(index)}
                disabled={disabled}
              />
            </Tooltip>
          </Space>
        </div>
      }
      style={{ marginBottom: 16 }}
    >
      {/* 分类标题显示/编辑 */}
      <div style={{ marginBottom: 12 }}>
        <Text strong>分类标题：</Text>
        {editingTitle ? (
          <Form form={titleForm} layout="inline" style={{ marginTop: 8 }}>
            <Form.Item
              name="zh"
              label="中文"
              rules={[{ required: true, message: '请输入中文标题' }]}
            >
              <Input placeholder="中文标题" />
            </Form.Item>
            <Form.Item
              name="en"
              label="英文"
              rules={[{ required: true, message: '请输入英文标题' }]}
            >
              <Input placeholder="English Title" />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" size="small" onClick={handleTitleSave}>
                  保存
                </Button>
                <Button size="small" onClick={() => setEditingTitle(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        ) : (
          <div style={{ marginTop: 4 }}>
            <Tag color="blue">{category.categoryTitle.zh}</Tag>
            <Tag color="green">{category.categoryTitle.en}</Tag>
          </div>
        )}
      </div>

      {/* 游戏列表 */}
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
          <Text strong>游戏列表：</Text>
          <Button
            type="dashed"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => setGameModalVisible(true)}
            disabled={disabled}
          >
            选择游戏
          </Button>
        </div>

        {selectedGames.length === 0 ? (
          <Empty 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无游戏，请点击选择游戏"
            style={{ margin: '20px 0' }}
          />
        ) : (
          <DragDropContext onDragEnd={handleGameReorder}>
            <Droppable droppableId={`category-${index}`}>
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {selectedGames.map((game, gameIndex) => (
                    <Draggable
                      key={game.id}
                      draggableId={`game-${game.id}`}
                      index={gameIndex}
                      isDragDisabled={disabled}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          style={{
                            ...provided.draggableProps.style,
                            marginBottom: 8,
                            padding: 8,
                            border: '1px solid #d9d9d9',
                            borderRadius: 4,
                            backgroundColor: snapshot.isDragging ? '#f0f0f0' : '#fff',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8
                          }}
                        >
                          <div {...provided.dragHandleProps}>
                            <DragOutlined style={{ color: '#999' }} />
                          </div>
                          <Avatar src={game.iconUrl} size="small" icon={<GamepadOutlined />} />
                          <div style={{ flex: 1 }}>
                            <div>{game.name}</div>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {game.categories?.join(', ')}
                            </Text>
                          </div>
                          <Text type="secondary">#{gameIndex + 1}</Text>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </div>

      {/* 游戏选择模态框 */}
      <GameSelectionModal
        visible={gameModalVisible}
        onCancel={() => setGameModalVisible(false)}
        onOk={handleGameSelection}
        applications={applications}
        selectedGameIds={category.games.map(g => g.applicationId)}
      />
    </Card>
  );
};

// 主组件
const GameCategoryConfig: React.FC<GameCategoryConfigProps> = ({
  value = [],
  onChange,
  applications = [],
  disabled = false
}) => {
  const [categories, setCategories] = useState<GameCategory[]>(value);

  useEffect(() => {
    setCategories(value);
  }, [value]);

  // 添加新分类组
  const handleAddCategory = () => {
    const newCategory: GameCategory = {
      categoryTitle: {
        zh: `分类组 ${categories.length + 1}`,
        en: `Category ${categories.length + 1}`
      },
      games: [],
      sortOrder: categories.length + 1
    };

    const newCategories = [...categories, newCategory];
    setCategories(newCategories);
    onChange?.(newCategories);
  };

  // 更新分类组
  const handleUpdateCategory = (index: number, category: GameCategory) => {
    const newCategories = [...categories];
    newCategories[index] = category;
    setCategories(newCategories);
    onChange?.(newCategories);
  };

  // 删除分类组
  const handleDeleteCategory = (index: number) => {
    const newCategories = categories.filter((_, i) => i !== index);
    // 重新排序
    const reorderedCategories = newCategories.map((cat, idx) => ({
      ...cat,
      sortOrder: idx + 1
    }));
    setCategories(reorderedCategories);
    onChange?.(reorderedCategories);
  };

  // 分类组拖拽排序
  const handleCategoryReorder = (result: any) => {
    if (!result.destination) return;

    const newCategories = Array.from(categories);
    const [reorderedItem] = newCategories.splice(result.source.index, 1);
    newCategories.splice(result.destination.index, 0, reorderedItem);

    // 更新排序
    const reorderedCategories = newCategories.map((cat, idx) => ({
      ...cat,
      sortOrder: idx + 1
    }));

    setCategories(reorderedCategories);
    onChange?.(reorderedCategories);
  };

  // 批量操作
  const handleBatchClear = () => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有分类组吗？此操作不可恢复。',
      onOk: () => {
        setCategories([]);
        onChange?.([]);
        message.success('已清空所有分类组');
      }
    });
  };

  const handleBatchAddDefault = () => {
    const defaultCategories: GameCategory[] = [
      {
        categoryTitle: { zh: '热门游戏', en: 'Popular Games' },
        games: [],
        sortOrder: 1
      },
      {
        categoryTitle: { zh: '最新游戏', en: 'New Games' },
        games: [],
        sortOrder: 2
      },
      {
        categoryTitle: { zh: '经典游戏', en: 'Classic Games' },
        games: [],
        sortOrder: 3
      },
      {
        categoryTitle: { zh: '推荐游戏', en: 'Recommended Games' },
        games: [],
        sortOrder: 4
      }
    ];

    setCategories(defaultCategories);
    onChange?.(defaultCategories);
    message.success('已添加默认分类组');
  };

  // 验证状态
  const getValidationStatus = () => {
    const errors: string[] = [];

    if (categories.length < 4) {
      errors.push('至少需要4个分类组');
    }

    categories.forEach((cat, index) => {
      if (!cat.categoryTitle.zh || !cat.categoryTitle.en) {
        errors.push(`第${index + 1}个分类组标题不完整`);
      }
      if (cat.games.length < 4) {
        errors.push(`第${index + 1}个分类组至少需要4个游戏`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const validation = getValidationStatus();

  return (
    <div>
      {/* 头部操作栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Title level={5} style={{ margin: 0 }}>
                <AppstoreOutlined /> 游戏分类组配置
              </Title>
              <Tag color={validation.isValid ? 'green' : 'red'}>
                {categories.length}/4+ 个分类组
              </Tag>
              {!validation.isValid && (
                <Tooltip title={validation.errors.join('; ')}>
                  <Tag color="red">验证失败</Tag>
                </Tooltip>
              )}
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="dashed"
                size="small"
                onClick={handleBatchAddDefault}
                disabled={disabled}
              >
                添加默认分类
              </Button>
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleAddCategory}
                disabled={disabled}
              >
                添加分类组
              </Button>
              <Button
                danger
                size="small"
                onClick={handleBatchClear}
                disabled={disabled || categories.length === 0}
              >
                清空全部
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 验证错误提示 */}
        {!validation.isValid && (
          <div style={{ marginTop: 12, padding: 8, backgroundColor: '#fff2f0', borderRadius: 4 }}>
            <Text type="danger">
              <strong>配置错误：</strong>
            </Text>
            <ul style={{ margin: '4px 0 0 20px', color: '#ff4d4f' }}>
              {validation.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </Card>

      {/* 分类组列表 */}
      {categories.length === 0 ? (
        <Card>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无分类组配置"
            style={{ margin: '40px 0' }}
          >
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddCategory}>
              添加第一个分类组
            </Button>
          </Empty>
        </Card>
      ) : (
        <DragDropContext onDragEnd={handleCategoryReorder}>
          <Droppable droppableId="categories">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {categories.map((category, index) => (
                  <Draggable
                    key={`category-${index}`}
                    draggableId={`category-${index}`}
                    index={index}
                    isDragDisabled={disabled}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        style={{
                          ...provided.draggableProps.style,
                          marginBottom: 16
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
                          <div
                            {...provided.dragHandleProps}
                            style={{
                              marginTop: 12,
                              cursor: disabled ? 'not-allowed' : 'grab'
                            }}
                          >
                            <DragOutlined style={{ color: '#999', fontSize: 16 }} />
                          </div>
                          <div style={{ flex: 1 }}>
                            <CategoryItem
                              category={category}
                              index={index}
                              applications={applications}
                              onUpdate={handleUpdateCategory}
                              onDelete={handleDeleteCategory}
                              disabled={disabled}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </div>
  );
};

export default GameCategoryConfig;
