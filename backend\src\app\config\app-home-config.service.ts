import { 
  Injectable, 
  NotFoundException, 
  ConflictException, 
  BadRequestException 
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { 
  AppHomeConfig,
  AppHomeRecommendedGame,
  AppHomeGameCategory,
  AppHomeCategoryGame
} from './entities';
import { AdConfig } from './entities/ad-config.entity';
import { Application } from '../entities/application.entity';
import { 
  CreateAppHomeConfigDto,
  UpdateAppHomeConfigDto,
  QueryAppHomeConfigDto,
  AppHomeConfigDetailResponseDto,
  AppHomeConfigListResponseDto,
  AppHomeConfigPageResponseDto
} from './dto';

@Injectable()
export class AppHomeConfigService {
  constructor(
    @InjectRepository(AppHomeConfig)
    private appHomeConfigRepository: Repository<AppHomeConfig>,
    @InjectRepository(AppHomeRecommendedGame)
    private recommendedGameRepository: Repository<AppHomeRecommendedGame>,
    @InjectRepository(AppHomeGameCategory)
    private gameCategoryRepository: Repository<AppHomeGameCategory>,
    @InjectRepository(AppHomeCategoryGame)
    private categoryGameRepository: Repository<AppHomeCategoryGame>,
    @InjectRepository(AdConfig)
    private adConfigRepository: Repository<AdConfig>,
    @InjectRepository(Application)
    private applicationRepository: Repository<Application>,
    private dataSource: DataSource,
  ) {}

  async create(createDto: CreateAppHomeConfigDto, userId: number) {
    // 检查配置名称是否已存在
    const existingConfig = await this.appHomeConfigRepository.findOne({
      where: { configName: createDto.configName },
    });
    if (existingConfig) {
      throw new ConflictException(`配置名称 ${createDto.configName} 已存在`);
    }

    // 验证广告配置是否存在
    await this.validateAdConfigs(createDto);

    // 验证BOX模式特殊要求
    await this.validateBoxModeConfig(createDto);

    // 验证游戏应用是否存在
    await this.validateApplications(createDto);

    // 使用事务创建配置
    return await this.dataSource.transaction(async manager => {
      // 创建主配置
      const homeConfig = manager.create(AppHomeConfig, {
        configName: createDto.configName,
        description: createDto.description,
        topBannerAdId: createDto.topBannerAdId,
        carouselAdId: createDto.carouselAdId,
        homeGridAdId: createDto.homeGridAdId,
        splashPopupAdId: createDto.splashPopupAdId,
        floatAdId: createDto.floatAdId,
        templateType: createDto.templateType ?? 'box',
        floatContents: createDto.floatContents ?? [],
        status: createDto.status ?? 1,
        sortOrder: createDto.sortOrder ?? 0,
        remark: createDto.remark,
        createdBy: userId,
        updatedBy: userId,
      });

      const savedConfig = await manager.save(AppHomeConfig, homeConfig);

      // 创建推荐游戏关联
      if (createDto.recommendedGames?.length) {
        const recommendedGames = createDto.recommendedGames.map((game, index) => 
          manager.create(AppHomeRecommendedGame, {
            homeConfigId: savedConfig.id,
            applicationId: game.applicationId,
            sortOrder: game.sortOrder ?? index + 1,
            createdBy: userId,
            updatedBy: userId,
          })
        );
        await manager.save(AppHomeRecommendedGame, recommendedGames);
      }

      // 创建游戏分类组
      if (createDto.gameCategories?.length) {
        for (const [categoryIndex, category] of createDto.gameCategories.entries()) {
          const gameCategory = manager.create(AppHomeGameCategory, {
            homeConfigId: savedConfig.id,
            categoryTitle: category.categoryTitle,
            sortOrder: category.sortOrder ?? categoryIndex + 1,
            createdBy: userId,
            updatedBy: userId,
          });

          const savedCategory = await manager.save(AppHomeGameCategory, gameCategory);

          // 创建分类下的游戏关联
          if (category.games?.length) {
            const categoryGames = category.games.map((game, gameIndex) =>
              manager.create(AppHomeCategoryGame, {
                homeCategoryId: savedCategory.id,
                applicationId: game.applicationId,
                sortOrder: game.sortOrder ?? gameIndex + 1,
                createdBy: userId,
                updatedBy: userId,
              })
            );
            await manager.save(AppHomeCategoryGame, categoryGames);
          }
        }
      }

      return savedConfig;
    });
  }

  async findAll(query: QueryAppHomeConfigDto) {
    const { 
      page = 1, 
      pageSize = 10, 
      configName, 
      status, 
      sortBy = 'createTime', 
      sortOrder = 'DESC' 
    } = query;

    const queryBuilder = this.appHomeConfigRepository
      .createQueryBuilder('config')
      .leftJoinAndSelect('config.creator', 'creator')
      .leftJoinAndSelect('config.updater', 'updater')
      .leftJoin('config.recommendedGames', 'recommendedGames')
      .leftJoin('config.gameCategories', 'gameCategories')
      .addSelect('COUNT(DISTINCT recommendedGames.id)', 'recommendedGameCount')
      .addSelect('COUNT(DISTINCT gameCategories.id)', 'categoryCount')
      .groupBy('config.id')
      .addGroupBy('creator.id')
      .addGroupBy('updater.id');

    // 添加筛选条件
    if (configName) {
      queryBuilder.andWhere('config.configName ILIKE :configName', { 
        configName: `%${configName}%` 
      });
    }

    if (status !== undefined) {
      queryBuilder.andWhere('config.status = :status', { status });
    }

    // 添加排序
    const orderDirection = sortOrder.toUpperCase() as 'ASC' | 'DESC';
    switch (sortBy) {
      case 'createTime':
        queryBuilder.orderBy('config.createTime', orderDirection);
        break;
      case 'updateTime':
        queryBuilder.orderBy('config.updateTime', orderDirection);
        break;
      case 'sortOrder':
        queryBuilder.orderBy('config.sortOrder', orderDirection);
        break;
      case 'configName':
        queryBuilder.orderBy('config.configName', orderDirection);
        break;
      default:
        queryBuilder.orderBy('config.createTime', 'DESC');
    }

    // 分页
    const offset = (page - 1) * pageSize;
    queryBuilder.skip(offset).take(pageSize);

    const [configs, total] = await queryBuilder.getManyAndCount();

    const list: AppHomeConfigListResponseDto[] = configs.map(config => ({
      id: config.id,
      configName: config.configName,
      description: config.description,
      status: config.status,
      sortOrder: config.sortOrder,
      templateType: config.templateType,
      recommendedGameCount: parseInt((config as any).recommendedGameCount) || 0,
      categoryCount: parseInt((config as any).categoryCount) || 0,
      createTime: config.createTime,
      updateTime: config.updateTime,
    }));

    const totalPages = Math.ceil(total / pageSize);

    return {
      list,
      total,
      page,
      pageSize,
      totalPages,
    } as AppHomeConfigPageResponseDto;
  }

  async findOne(id: number): Promise<AppHomeConfigDetailResponseDto> {
    const config = await this.appHomeConfigRepository.findOne({
      where: { id },
      relations: [
        'creator',
        'updater',
        'topBannerAd',
        'carouselAd',
        'homeGridAd',
        'splashPopupAd',
        'floatAd',
        'recommendedGames',
        'recommendedGames.application',
        'gameCategories',
        'gameCategories.categoryGames',
        'gameCategories.categoryGames.application',
      ],
      order: {
        recommendedGames: { sortOrder: 'ASC' },
        gameCategories: { 
          sortOrder: 'ASC',
          categoryGames: { sortOrder: 'ASC' }
        },
      },
    });

    if (!config) {
      throw new NotFoundException(`ID为 ${id} 的首页配置不存在`);
    }

    return this.transformToDetailResponse(config);
  }

  async update(id: number, updateDto: UpdateAppHomeConfigDto, userId: number) {
    const config = await this.appHomeConfigRepository.findOne({ where: { id } });
    if (!config) {
      throw new NotFoundException(`ID为 ${id} 的首页配置不存在`);
    }

    // 如果更新配置名称，检查是否重复
    if (updateDto.configName && updateDto.configName !== config.configName) {
      const existingConfig = await this.appHomeConfigRepository.findOne({
        where: { configName: updateDto.configName },
      });
      if (existingConfig) {
        throw new ConflictException(`配置名称 ${updateDto.configName} 已存在`);
      }
    }

    // 验证广告配置
    if (this.hasAdConfigUpdates(updateDto)) {
      await this.validateAdConfigs(updateDto);
    }

    // 验证BOX模式特殊要求
    if (updateDto.templateType || updateDto.gameCategories || updateDto.floatContents !== undefined) {
      // 构造验证用的DTO对象
      const validationDto: Partial<CreateAppHomeConfigDto> = {
        templateType: updateDto.templateType || config.templateType,
        homeGridAdId: updateDto.homeGridAdId !== undefined ? updateDto.homeGridAdId : config.homeGridAdId,
        gameCategories: updateDto.gameCategories || [],
        floatContents: updateDto.floatContents !== undefined ? updateDto.floatContents : config.floatContents,
      };
      await this.validateBoxModeConfig(validationDto as CreateAppHomeConfigDto);
    }

    // 验证游戏应用
    if (updateDto.recommendedGames || updateDto.gameCategories) {
      await this.validateApplications(updateDto);
    }

    return await this.dataSource.transaction(async manager => {
      // 更新主配置
      Object.assign(config, {
        ...updateDto,
        updatedBy: userId,
      });
      const savedConfig = await manager.save(AppHomeConfig, config);

      // 更新推荐游戏关联
      if (updateDto.recommendedGames) {
        await manager.delete(AppHomeRecommendedGame, { homeConfigId: id });
        
        if (updateDto.recommendedGames.length > 0) {
          const recommendedGames = updateDto.recommendedGames.map((game, index) =>
            manager.create(AppHomeRecommendedGame, {
              homeConfigId: id,
              applicationId: game.applicationId,
              sortOrder: game.sortOrder ?? index + 1,
              createdBy: userId,
              updatedBy: userId,
            })
          );
          await manager.save(AppHomeRecommendedGame, recommendedGames);
        }
      }

      // 更新游戏分类组
      if (updateDto.gameCategories) {
        // 删除现有的分类组和关联游戏
        const existingCategories = await manager.find(AppHomeGameCategory, {
          where: { homeConfigId: id },
        });
        
        for (const category of existingCategories) {
          await manager.delete(AppHomeCategoryGame, { homeCategoryId: category.id });
        }
        await manager.delete(AppHomeGameCategory, { homeConfigId: id });

        // 创建新的分类组
        if (updateDto.gameCategories.length > 0) {
          for (const [categoryIndex, category] of updateDto.gameCategories.entries()) {
            const gameCategory = manager.create(AppHomeGameCategory, {
              homeConfigId: id,
              categoryTitle: category.categoryTitle,
              sortOrder: category.sortOrder ?? categoryIndex + 1,
              createdBy: userId,
              updatedBy: userId,
            });

            const savedCategory = await manager.save(AppHomeGameCategory, gameCategory);

            if (category.games?.length) {
              const categoryGames = category.games.map((game, gameIndex) =>
                manager.create(AppHomeCategoryGame, {
                  homeCategoryId: savedCategory.id,
                  applicationId: game.applicationId,
                  sortOrder: game.sortOrder ?? gameIndex + 1,
                  createdBy: userId,
                  updatedBy: userId,
                })
              );
              await manager.save(AppHomeCategoryGame, categoryGames);
            }
          }
        }
      }

      return savedConfig;
    });
  }

  async remove(id: number) {
    const config = await this.appHomeConfigRepository.findOne({ where: { id } });
    if (!config) {
      throw new NotFoundException(`ID为 ${id} 的首页配置不存在`);
    }

    await this.dataSource.transaction(async manager => {
      // 删除分类组游戏关联
      const categories = await manager.find(AppHomeGameCategory, {
        where: { homeConfigId: id },
      });
      
      for (const category of categories) {
        await manager.delete(AppHomeCategoryGame, { homeCategoryId: category.id });
      }

      // 删除分类组
      await manager.delete(AppHomeGameCategory, { homeConfigId: id });

      // 删除推荐游戏关联
      await manager.delete(AppHomeRecommendedGame, { homeConfigId: id });

      // 删除主配置
      await manager.remove(AppHomeConfig, config);
    });

    return { message: '删除成功' };
  }

  async toggleStatus(id: number, userId: number) {
    const config = await this.appHomeConfigRepository.findOne({ where: { id } });
    if (!config) {
      throw new NotFoundException(`ID为 ${id} 的首页配置不存在`);
    }

    config.status = config.status === 1 ? 0 : 1;
    config.updatedBy = userId;

    await this.appHomeConfigRepository.save(config);
    return {
      message: `${config.status === 1 ? '启用' : '禁用'}成功`,
      status: config.status
    };
  }

  // APP端API：根据ID获取首页配置数据
  async getAppHomeConfig(id: number) {
    const config = await this.appHomeConfigRepository.findOne({
      where: { id, status: 1 }, // 只返回启用的配置
      relations: [
        'topBannerAd',
        'carouselAd',
        'homeGridAd',
        'splashPopupAd',
        'floatAd',
        'recommendedGames',
        'recommendedGames.application',
        'gameCategories',
        'gameCategories.categoryGames',
        'gameCategories.categoryGames.application',
      ],
      order: {
        recommendedGames: { sortOrder: 'ASC' },
        gameCategories: {
          sortOrder: 'ASC',
          categoryGames: { sortOrder: 'ASC' }
        },
      },
    });

    if (!config) {
      throw new NotFoundException(`ID为 ${id} 的首页配置不存在或已禁用`);
    }

    return this.transformToAppResponse(config);
  }

  // 私有辅助方法
  private async validateAdConfigs(dto: CreateAppHomeConfigDto | UpdateAppHomeConfigDto) {
    const adIds = [
      dto.topBannerAdId,
      dto.carouselAdId,
      dto.homeGridAdId,
      dto.splashPopupAdId,
      dto.floatAdId,
    ].filter(id => id !== undefined && id !== null);

    if (adIds.length > 0) {
      const existingAds = await this.adConfigRepository.findByIds(adIds);
      const existingAdIds = existingAds.map(ad => ad.id);

      for (const adId of adIds) {
        if (!existingAdIds.includes(adId)) {
          throw new BadRequestException(`广告配置ID ${adId} 不存在`);
        }
      }

      // 检查启用状态
      const disabledAds = existingAds.filter(ad => ad.status === 0);
      if (disabledAds.length > 0) {
        throw new BadRequestException(
          `以下广告配置已禁用：${disabledAds.map(ad => ad.title).join(', ')}`
        );
      }
    }
  }

  private async validateApplications(dto: CreateAppHomeConfigDto | UpdateAppHomeConfigDto) {
    const applicationIds: number[] = [];

    // 收集推荐游戏ID
    if (dto.recommendedGames) {
      applicationIds.push(...dto.recommendedGames.map(game => game.applicationId));
    }

    // 收集分类组游戏ID
    if (dto.gameCategories) {
      for (const category of dto.gameCategories) {
        if (category.games) {
          applicationIds.push(...category.games.map(game => game.applicationId));
        }
      }
    }

    if (applicationIds.length > 0) {
      const uniqueIds = [...new Set(applicationIds)];
      const existingApps = await this.applicationRepository.findByIds(uniqueIds);
      const existingAppIds = existingApps.map(app => app.id);

      for (const appId of uniqueIds) {
        if (!existingAppIds.includes(appId)) {
          throw new BadRequestException(`游戏应用ID ${appId} 不存在`);
        }
      }

      // 检查游戏状态
      const inactiveApps = existingApps.filter(app => app.status !== 'active');
      if (inactiveApps.length > 0) {
        throw new BadRequestException(
          `以下游戏应用未激活：${inactiveApps.map(app => app.name).join(', ')}`
        );
      }
    }
  }

  private hasAdConfigUpdates(dto: UpdateAppHomeConfigDto): boolean {
    return !!(
      dto.topFloatAdId !== undefined ||
      dto.carouselAdId !== undefined ||
      dto.homeGridAdId !== undefined ||
      dto.splashPopupAdId !== undefined ||
      dto.floatAdId !== undefined
    );
  }

  private transformToDetailResponse(config: AppHomeConfig): AppHomeConfigDetailResponseDto {
    return {
      id: config.id,
      configName: config.configName,
      description: config.description,
      status: config.status,
      sortOrder: config.sortOrder,
      remark: config.remark,
      templateType: config.templateType,
      floatContents: config.floatContents || [],
      createTime: config.createTime,
      updateTime: config.updateTime,

      // 广告配置
      topFloatAd: config.topFloatAd ? this.transformAdInfo(config.topFloatAd) : undefined,
      carouselAd: config.carouselAd ? this.transformAdInfo(config.carouselAd) : undefined,
      homeGridAd: config.homeGridAd ? this.transformAdInfo(config.homeGridAd) : undefined,
      splashPopupAd: config.splashPopupAd ? this.transformAdInfo(config.splashPopupAd) : undefined,
      floatAd: config.floatAd ? this.transformAdInfo(config.floatAd) : undefined,

      // 推荐游戏
      recommendedGames: config.recommendedGames?.map(rg => ({
        id: rg.id,
        sortOrder: rg.sortOrder,
        game: this.transformGameInfo(rg.application),
      })) || [],

      // 游戏分类组
      gameCategories: config.gameCategories?.map(gc => ({
        id: gc.id,
        categoryTitle: gc.categoryTitle,
        sortOrder: gc.sortOrder,
        status: gc.status,
        games: gc.categoryGames?.map(cg => ({
          id: cg.id,
          sortOrder: cg.sortOrder,
          game: this.transformGameInfo(cg.application),
        })) || [],
      })) || [],
    };
  }

  private transformToAppResponse(config: AppHomeConfig) {
    return {
      id: config.id,
      configName: config.configName,
      templateType: config.templateType,

      // 浮点内容配置（只返回启用的）
      floatContents: config.floatContents?.filter(content => content.status === 1) || [],

      // 广告配置（只返回启用的）
      ads: {
        topFloat: config.topFloatAd?.status === 1 ? this.transformAdInfo(config.topFloatAd) : null,
        carousel: config.carouselAd?.status === 1 ? this.transformAdInfo(config.carouselAd) : null,
        homeGrid: config.homeGridAd?.status === 1 ? this.transformAdInfo(config.homeGridAd) : null,
        splashPopup: config.splashPopupAd?.status === 1 ? this.transformAdInfo(config.splashPopupAd) : null,
        float: config.floatAd?.status === 1 ? this.transformAdInfo(config.floatAd) : null,
      },

      // 推荐游戏（只返回启用的）
      recommendedGames: config.recommendedGames
        ?.filter(rg => rg.status === 1 && rg.application?.status === 'active')
        .map(rg => this.transformGameInfo(rg.application)) || [],

      // 游戏分类组（只返回启用的）
      gameCategories: config.gameCategories
        ?.filter(gc => gc.status === 1)
        .map(gc => ({
          id: gc.id,
          categoryTitle: gc.categoryTitle,
          games: gc.categoryGames
            ?.filter(cg => cg.status === 1 && cg.application?.status === 'active')
            .map(cg => this.transformGameInfo(cg.application)) || [],
        })) || [],
    };
  }

  private transformAdInfo(ad: AdConfig) {
    return {
      id: ad.id,
      adIdentifier: ad.adIdentifier,
      title: ad.title,
      adType: ad.adType,
      imageItems: ad.imageItems,
    };
  }

  private transformGameInfo(app: Application) {
    return {
      id: app.id,
      name: app.name,
      iconUrl: app.iconUrl,
      posterUrl: app.posterUrl,
      categories: app.categories || [],
      rtp: app.rtp,
      volatility: app.volatility,
      status: app.status,
    };
  }

  private async validateBoxModeConfig(dto: CreateAppHomeConfigDto | UpdateAppHomeConfigDto) {
    const templateType = dto.templateType || 'box';

    if (templateType === 'box') {
      // BOX模式需要6宫格广告
      if (!dto.homeGridAdId) {
        throw new BadRequestException('BOX模式需要配置6宫格广告');
      }

      // 检查游戏分类组数量（至少4个）
      if (!dto.gameCategories || dto.gameCategories.length < 4) {
        throw new BadRequestException('BOX模式至少需要4个游戏分类组');
      }

      // 检查每个分类的游戏数量（至少4个）
      dto.gameCategories.forEach((category, index) => {
        if (!category.games || category.games.length < 4) {
          throw new BadRequestException(`第${index + 1}个游戏分类至少需要4个游戏`);
        }
      });

      // 检查浮点内容数量（最多3个）
      if (dto.floatContents && dto.floatContents.length > 3) {
        throw new BadRequestException('BOX模式浮点内容最多3个');
      }

      // 验证浮点内容的唯一性
      if (dto.floatContents && dto.floatContents.length > 0) {
        const ids = dto.floatContents.map(content => content.id);
        const uniqueIds = new Set(ids);
        if (ids.length !== uniqueIds.size) {
          throw new BadRequestException('浮点内容ID必须唯一');
        }
      }
    }
  }
}
