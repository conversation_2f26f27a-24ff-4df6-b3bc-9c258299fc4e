{"name": "react-beautiful-dnd", "version": "13.1.1", "description": "Beautiful and accessible drag and drop for lists with React", "author": "<PERSON> <<EMAIL>>", "keywords": ["drag and drop", "dnd", "sortable", "reorder", "reorderable", "react", "react.js", "natural", "beautiful", "accessible"], "repository": {"type": "git", "url": "https://github.com/atlassian/react-beautiful-dnd.git"}, "bugs": {"url": "https://github.com/atlassian/react-beautiful-dnd/issues"}, "main": "dist/react-beautiful-dnd.cjs.js", "module": "dist/react-beautiful-dnd.esm.js", "sideEffects": false, "files": ["/dist", "/src"], "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json} docs/**/*.{js,jsx,md,json} stories/**/*.{js,jsx,md,json} cypress/**/*.{js,jsx,md,json} csp-server/**/*.{js,jsx,md,json}"}, "scripts": {"test:accessibility": "lighthouse http://localhost:9002/iframe.html?id=single-vertical-list--basic --chrome-flags='--headless' --output=json --output=html --output-path=./test-reports/lighthouse/a11y.json && node a11y-audit-parse.js", "test": "jest --config ./jest.config.js", "test:ci": "jest test --maxWorkers=2", "test:browser": "cypress open", "test:browser:ci": "cypress run", "test:coverage": "yarn test --coverage --coveragePathIgnorePatterns=/debug", "validate": "yarn prettier:check && yarn lint:eslint && yarn lint:css && yarn typecheck", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "yarn eslint \"./**/*.{js,jsx}\"", "lint:css": "stylelint \"stories/**/*.{js,jsx}\"", "typecheck": "flow check --max-warnings=0", "bundle-size:check": "cross-env SNAPSHOT=match yarn bundle-size:update", "bundle-size:update": "yarn build:clean && yarn build:dist && yarn build:clean", "build": "yarn build:clean && yarn build:dist && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/react-beautiful-dnd.cjs.js.flow", "storybook": "start-storybook -p 9002", "build-storybook": "build-storybook -c .storybook -o site", "prepublishOnly": "yarn build"}, "dependencies": {"@babel/runtime": "^7.9.2", "css-box-model": "^1.2.0", "memoize-one": "^5.1.1", "raf-schd": "^4.0.2", "react-redux": "^7.2.0", "redux": "^4.0.4", "use-memo-one": "^1.1.1"}, "devDependencies": {"@atlaskit/css-reset": "5.0.12", "@atlaskit/theme": "9.5.6", "@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.8.3", "@babel/plugin-transform-modules-commonjs": "7.9.0", "@babel/plugin-transform-object-assign": "7.8.3", "@babel/plugin-transform-runtime": "7.9.0", "@babel/preset-env": "7.9.5", "@babel/preset-flow": "7.9.0", "@babel/preset-react": "7.9.4", "@emotion/babel-preset-css-prop": "10.0.27", "@emotion/core": "10.0.28", "@emotion/styled": "10.0.27", "@storybook/addon-storysource": "5.3.21", "@storybook/react": "5.3.21", "@storybook/theming": "5.3.21", "@testing-library/react": "10.4.9", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.1.0", "babel-jest": "25.5.1", "babel-loader": "8.1.0", "babel-plugin-dev-expression": "0.2.2", "babel-plugin-emotion": "10.0.33", "cross-env": "7.0.3", "cypress": "4.4.0", "emotion-theming": "10.0.27", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.2", "eslint": "6.8.0", "eslint-config-airbnb": "18.1.0", "eslint-config-prettier": "6.15.0", "eslint-plugin-cypress": "2.11.3", "eslint-plugin-emotion": "10.0.27", "eslint-plugin-es5": "1.5.0", "eslint-plugin-flowtype": "4.7.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jest": "23.20.0", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "3.0.0", "flow-bin": "0.110.1", "fs-extra": "9.1.0", "globby": "11.0.3", "jest": "25.5.4", "jest-axe": "3.5.0", "jest-junit": "10.0.0", "jest-watch-typeahead": "0.6.1", "lighthouse": "5.6.0", "markdown-it": "10.0.0", "prettier": "2.0.4", "raf-stub": "3.0.0", "react": "16.13.1", "react-dom": "16.13.1", "react-test-renderer": "16.13.1", "react-virtualized": "9.21.2", "react-window": "1.8.6", "require-from-string": "2.0.2", "rimraf": "3.0.2", "rollup": "2.6.1", "rollup-plugin-babel": "4.4.0", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-json": "4.0.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-size-snapshot": "0.11.0", "rollup-plugin-strip": "1.2.2", "rollup-plugin-terser": "5.3.1", "storybook-addon-performance": "0.9.0", "styled-components": "5.1.0", "stylelint": "13.13.1", "stylelint-config-prettier": "8.0.2", "stylelint-config-recommended": "3.0.0", "stylelint-config-standard": "20.0.0", "stylelint-config-styled-components": "0.1.1", "stylelint-processor-styled-components": "1.10.0", "wait-port": "0.2.9", "webpack": "4.42.1"}, "peerDependencies": {"react": "^16.8.5 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.5 || ^17.0.0 || ^18.0.0"}, "license": "Apache-2.0", "jest-junit": {"output": "test-reports/junit/js-test-results.xml"}}