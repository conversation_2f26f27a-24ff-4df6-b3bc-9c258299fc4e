import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { SysUser } from '../../../system/entities/sys-user.entity';
import { AdConfig } from './ad-config.entity';
import { AppHomeRecommendedGame } from './app-home-recommended-game.entity';
import { AppHomeGameCategory } from './app-home-game-category.entity';

// 浮点内容接口
export interface FloatContent {
  id: string;                    // 唯一标识
  type: string;                  // 内容类型：announcement-公告，promotion-活动，customer_service-客服，checkin_reminder-签到，recharge_bonus-充值优惠
  title: string;                 // 标题
  content: string;               // 内容
  imageUrl?: string;             // 图片URL
  jumpType?: 'internal_route' | 'iframe_page';  // 跳转类型
  jumpTarget?: string;           // 跳转目标
  position: {                    // 位置配置
    x: number;                   // X坐标（百分比）
    y: number;                   // Y坐标（百分比）
  };
  style?: {                      // 样式配置
    width?: number;              // 宽度
    height?: number;             // 高度
    backgroundColor?: string;    // 背景色
    textColor?: string;          // 文字颜色
    borderRadius?: number;       // 圆角
  };
  displayTime?: {                // 显示时间配置
    startTime?: string;          // 开始时间
    endTime?: string;            // 结束时间
    duration?: number;           // 显示持续时间（秒）
  };
  sortOrder: number;             // 排序
  status: number;                // 状态：1-启用，0-禁用
}

@Entity('app_home_configs')
export class AppHomeConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'config_name', length: 100, comment: '配置名称' })
  configName: string;

  @Column({ nullable: true, length: 500, comment: '配置描述' })
  description: string;

  // 广告配置关联
  @Column({ name: 'top_float_ad_id', nullable: true, comment: '顶部浮动广告ID（必填）' })
  topFloatAdId: number;

  @Column({ name: 'carousel_ad_id', nullable: true, comment: '轮播广告ID（必填）' })
  carouselAdId: number;

  @Column({ name: 'home_grid_ad_id', nullable: true, comment: '首页九宫格ID（可选）' })
  homeGridAdId: number;

  @Column({ name: 'splash_popup_ad_id', nullable: true, comment: '开屏弹窗广告ID（可选）' })
  splashPopupAdId: number;

  @Column({ name: 'float_ad_id', nullable: true, comment: '浮点广告ID（可选）' })
  floatAdId: number;

  // 模板配置
  @Column({
    name: 'template_type',
    length: 50,
    default: 'box',
    comment: '首页模板类型：box-BOX模式，classic-经典模式，card-卡片模式，waterfall-瀑布流模式'
  })
  templateType: string;

  // 浮点内容配置
  @Column({
    name: 'float_contents',
    type: 'jsonb',
    default: () => "'[]'",
    comment: '浮点内容配置数组，包含公告、活动、客服等浮点内容'
  })
  floatContents: FloatContent[];

  @Column({ default: 1, comment: '状态：1-启用，0-禁用' })
  status: number;

  @Column({ name: 'sort_order', default: 0, comment: '排序，数字越小越靠前' })
  sortOrder: number;

  @Column({ nullable: true, length: 500, comment: '备注说明' })
  remark: string;

  @Column({ name: 'created_by', nullable: true, comment: '创建人ID' })
  createdBy: number;

  @Column({ name: 'updated_by', nullable: true, comment: '最后更新人ID' })
  updatedBy: number;

  @CreateDateColumn({ name: 'create_time' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time' })
  updateTime: Date;

  // 关联创建人
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator: SysUser;

  // 关联更新人
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: SysUser;

  // 关联广告配置
  @ManyToOne(() => AdConfig, { nullable: true })
  @JoinColumn({ name: 'top_float_ad_id' })
  topFloatAd: AdConfig;

  @ManyToOne(() => AdConfig, { nullable: true })
  @JoinColumn({ name: 'carousel_ad_id' })
  carouselAd: AdConfig;

  @ManyToOne(() => AdConfig, { nullable: true })
  @JoinColumn({ name: 'home_grid_ad_id' })
  homeGridAd: AdConfig;

  @ManyToOne(() => AdConfig, { nullable: true })
  @JoinColumn({ name: 'splash_popup_ad_id' })
  splashPopupAd: AdConfig;

  @ManyToOne(() => AdConfig, { nullable: true })
  @JoinColumn({ name: 'float_ad_id' })
  floatAd: AdConfig;

  // 关联推荐游戏
  @OneToMany(() => AppHomeRecommendedGame, (recommendedGame) => recommendedGame.homeConfig)
  recommendedGames: AppHomeRecommendedGame[];

  // 关联游戏分类组
  @OneToMany(() => AppHomeGameCategory, (gameCategory) => gameCategory.homeConfig)
  gameCategories: AppHomeGameCategory[];

  // 业务方法
  isEnabled(): boolean {
    return this.status === 1;
  }

  hasRequiredAds(): boolean {
    // 检查必填的广告配置是否存在
    if (this.templateType === 'box') {
      // BOX模式需要顶部浮动广告和轮播广告
      return !!(this.topFloatAdId && this.carouselAdId);
    }
    // 其他模式的必填广告配置
    return !!(this.topFloatAdId && this.carouselAdId);
  }

  getAdCount(): number {
    let count = 0;
    if (this.topFloatAdId) count++;
    if (this.carouselAdId) count++;
    if (this.homeGridAdId) count++;
    if (this.splashPopupAdId) count++;
    if (this.floatAdId) count++;
    return count;
  }

  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必填字段
    if (!this.configName?.trim()) {
      errors.push('配置名称不能为空');
    }

    if (!this.topFloatAdId) {
      errors.push('顶部浮动广告为必填项');
    }

    if (!this.carouselAdId) {
      errors.push('轮播广告为必填项');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // 模板相关方法
  isBoxMode(): boolean {
    return this.templateType === 'box';
  }

  isClassicMode(): boolean {
    return this.templateType === 'classic';
  }

  getFloatContentCount(): number {
    return this.floatContents?.length || 0;
  }

  getActiveFloatContents(): FloatContent[] {
    return this.floatContents?.filter(content => content.status === 1) || [];
  }

  validateBoxModeConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.templateType === 'box') {
      // BOX模式特殊验证
      if (!this.homeGridAdId) {
        errors.push('BOX模式需要配置6宫格广告');
      }

      // 检查游戏分类组数量（至少4个）
      if (!this.gameCategories || this.gameCategories.length < 4) {
        errors.push('BOX模式至少需要4个游戏分类组');
      }

      // 检查每个分类的游戏数量（至少4个）
      if (this.gameCategories) {
        this.gameCategories.forEach((category, index) => {
          if (!category.categoryGames || category.categoryGames.length < 4) {
            errors.push(`第${index + 1}个游戏分类至少需要4个游戏`);
          }
        });
      }

      // 检查浮点内容数量（最多3个）
      if (this.floatContents && this.floatContents.length > 3) {
        errors.push('BOX模式浮点内容最多3个');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
