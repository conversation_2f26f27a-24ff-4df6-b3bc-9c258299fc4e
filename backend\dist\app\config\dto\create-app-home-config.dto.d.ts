import { CategoryTitle } from '../entities/app-home-game-category.entity';
export declare class RecommendedGameDto {
    applicationId: number;
    sortOrder?: number;
}
export declare class CategoryGameDto {
    applicationId: number;
    sortOrder?: number;
}
export declare class GameCategoryDto {
    categoryTitle: CategoryTitle;
    sortOrder?: number;
    games: CategoryGameDto[];
}
export declare class CreateAppHomeConfigDto {
    configName: string;
    description?: string;
    topBannerAdId: number;
    carouselAdId: number;
    homeGridAdId?: number;
    splashPopupAdId?: number;
    templateType?: string;
    recommendedGames: RecommendedGameDto[];
    gameCategories: GameCategoryDto[];
    sortOrder?: number;
    status?: number;
    remark?: string;
}
