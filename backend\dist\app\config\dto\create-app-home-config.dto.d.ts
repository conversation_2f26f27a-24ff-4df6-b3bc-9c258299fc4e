import { CategoryTitle } from '../entities/app-home-game-category.entity';
export declare class FloatContentDto {
    id: string;
    type: string;
    title: string;
    content: string;
    imageUrl?: string;
    jumpType?: 'internal_route' | 'iframe_page';
    jumpTarget?: string;
    position: {
        x: number;
        y: number;
    };
    style?: {
        width?: number;
        height?: number;
        backgroundColor?: string;
        textColor?: string;
        borderRadius?: number;
    };
    displayTime?: {
        startTime?: string;
        endTime?: string;
        duration?: number;
    };
    sortOrder: number;
    status: number;
}
export declare class RecommendedGameDto {
    applicationId: number;
    sortOrder?: number;
}
export declare class CategoryGameDto {
    applicationId: number;
    sortOrder?: number;
}
export declare class GameCategoryDto {
    categoryTitle: CategoryTitle;
    sortOrder?: number;
    games: CategoryGameDto[];
}
export declare class CreateAppHomeConfigDto {
    configName: string;
    description?: string;
    topBannerAdId: number;
    carouselAdId: number;
    homeGridAdId?: number;
    splashPopupAdId?: number;
    floatAdId?: number;
    templateType?: string;
    floatContents?: FloatContentDto[];
    recommendedGames: RecommendedGameDto[];
    gameCategories: GameCategoryDto[];
    sortOrder?: number;
    status?: number;
    remark?: string;
}
